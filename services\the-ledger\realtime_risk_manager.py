#!/usr/bin/env python3
"""
Real-time Risk Manager with Binance WebSocket Integration
========================================================

Enhanced risk management system that uses Binance WebSocket for real-time
position monitoring, instant stop-loss execution, and dynamic risk adjustment.

Features:
- Real-time price monitoring via WebSocket
- Sub-second risk rule evaluation
- Instant stop-loss and take-profit execution
- Dynamic position sizing based on volatility
- Advanced risk metrics and alerts
"""

import os
import sys
import json
import time
import logging
import threading
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(__file__))

from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
from risk_manager import check_risk_rules, calculate_position_metrics
from db_handler import get_open_positions, update_position_status

@dataclass
class RiskAlert:
    """Real-time risk alert"""
    position_id: int
    token_symbol: str
    alert_type: str  # 'STOP_LOSS', 'TAKE_PROFIT', 'HIGH_RISK', 'VOLATILITY'
    current_price: Decimal
    trigger_price: Decimal
    risk_level: str  # 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    action_required: str  # 'MONITOR', 'CLOSE_POSITION', 'REDUCE_SIZE'
    timestamp: datetime
    message: str

class RealTimeRiskManager:
    """Enhanced risk manager with WebSocket integration"""
    
    def __init__(self):
        self.websocket_feed = BinanceWebSocketPriceFeed()
        self.monitored_positions = {}  # position_id -> position_data
        self.price_alerts = {}         # position_id -> alert_thresholds
        self.risk_callbacks = []       # List of callback functions
        self.running = False
        self.monitoring_thread = None
        
        # Risk parameters
        self.stop_loss_pct = Decimal(os.environ.get("STOP_LOSS_PCT", "0.15"))
        self.take_profit_pct = Decimal(os.environ.get("TAKE_PROFIT_PCT", "0.10"))
        self.high_risk_threshold = Decimal("0.05")  # 5% from stop loss = high risk
        self.critical_risk_threshold = Decimal("0.02")  # 2% from stop loss = critical
        
        # Monitoring frequency
        self.check_interval = 5  # Check every 5 seconds
        self.price_update_interval = 1  # Update prices every second
        
        logging.info("🛡️ Real-time Risk Manager initialized")
    
    def add_position_to_monitor(self, position: Dict[str, Any]) -> bool:
        """Add a position to real-time monitoring"""
        try:
            position_id = position.get('position_id')
            token_address = position.get('token_address')
            token_symbol = position.get('token_symbol')
            
            if not all([position_id, token_address, token_symbol]):
                logging.error(f"Invalid position data: {position}")
                return False
            
            # Subscribe to WebSocket price feed
            success = self.websocket_feed.subscribe_to_price(token_address)
            
            if success:
                self.monitored_positions[position_id] = position
                self.setup_price_alerts(position)
                logging.info(f"🔍 Added position {position_id} ({token_symbol}) to real-time monitoring")
                return True
            else:
                logging.error(f"❌ Failed to subscribe to {token_symbol} WebSocket")
                return False
                
        except Exception as e:
            logging.error(f"Error adding position to monitor: {e}")
            return False
    
    def remove_position_from_monitor(self, position_id: int):
        """Remove a position from monitoring"""
        try:
            if position_id in self.monitored_positions:
                position = self.monitored_positions[position_id]
                token_address = position.get('token_address')
                token_symbol = position.get('token_symbol')
                
                self.websocket_feed.unsubscribe_from_price(token_address)
                del self.monitored_positions[position_id]
                
                if position_id in self.price_alerts:
                    del self.price_alerts[position_id]
                
                logging.info(f"🔇 Removed position {position_id} ({token_symbol}) from monitoring")
                
        except Exception as e:
            logging.error(f"Error removing position from monitor: {e}")
    
    def setup_price_alerts(self, position: Dict[str, Any]):
        """Setup price alert thresholds for a position"""
        try:
            position_id = position.get('position_id')
            entry_price = Decimal(str(position.get('entry_price_in_usdc', 0)))
            
            if entry_price <= 0:
                logging.error(f"Invalid entry price for position {position_id}")
                return
            
            # Calculate alert thresholds
            stop_loss_price = entry_price * (1 + self.stop_loss_pct)
            take_profit_price = entry_price * (1 - self.take_profit_pct)
            high_risk_price = entry_price * (1 + self.stop_loss_pct - self.high_risk_threshold)
            critical_risk_price = entry_price * (1 + self.stop_loss_pct - self.critical_risk_threshold)
            
            self.price_alerts[position_id] = {
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'high_risk': high_risk_price,
                'critical_risk': critical_risk_price,
                'entry_price': entry_price
            }
            
            logging.info(f"📊 Price alerts set for position {position_id}:")
            logging.info(f"   Entry: ${entry_price:.4f}")
            logging.info(f"   Take Profit: ${take_profit_price:.4f}")
            logging.info(f"   High Risk: ${high_risk_price:.4f}")
            logging.info(f"   Critical Risk: ${critical_risk_price:.4f}")
            logging.info(f"   Stop Loss: ${stop_loss_price:.4f}")
            
        except Exception as e:
            logging.error(f"Error setting up price alerts: {e}")
    
    def add_risk_callback(self, callback: Callable[[RiskAlert], None]):
        """Add a callback function for risk alerts"""
        self.risk_callbacks.append(callback)
        logging.info(f"📞 Added risk callback: {callback.__name__}")
    
    def check_position_realtime(self, position_id: int) -> Optional[RiskAlert]:
        """Check a position in real-time and generate alerts"""
        try:
            if position_id not in self.monitored_positions:
                return None
            
            position = self.monitored_positions[position_id]
            token_address = position.get('token_address')
            token_symbol = position.get('token_symbol')
            
            # Get current price from WebSocket
            current_price = self.websocket_feed.get_price(token_address)
            if not current_price:
                # Fallback to REST API
                current_price = get_realtime_price_binance(token_address)
                if not current_price:
                    logging.warning(f"No price data for {token_symbol}")
                    return None
            
            # Get alert thresholds
            alerts = self.price_alerts.get(position_id, {})
            if not alerts:
                logging.warning(f"No alerts set for position {position_id}")
                return None
            
            # Check for risk conditions
            alert = self.evaluate_risk_conditions(
                position_id, token_symbol, current_price, alerts
            )
            
            return alert
            
        except Exception as e:
            logging.error(f"Error checking position {position_id}: {e}")
            return None
    
    def evaluate_risk_conditions(self, position_id: int, token_symbol: str, 
                                current_price: Decimal, alerts: Dict) -> Optional[RiskAlert]:
        """Evaluate risk conditions and create alerts"""
        try:
            stop_loss_price = alerts['stop_loss']
            take_profit_price = alerts['take_profit']
            high_risk_price = alerts['high_risk']
            critical_risk_price = alerts['critical_risk']
            
            # Check stop loss (highest priority)
            if current_price >= stop_loss_price:
                return RiskAlert(
                    position_id=position_id,
                    token_symbol=token_symbol,
                    alert_type='STOP_LOSS',
                    current_price=current_price,
                    trigger_price=stop_loss_price,
                    risk_level='CRITICAL',
                    action_required='CLOSE_POSITION',
                    timestamp=datetime.now(timezone.utc),
                    message=f"STOP LOSS TRIGGERED: Price ${current_price:.4f} >= ${stop_loss_price:.4f}"
                )
            
            # Check take profit
            if current_price <= take_profit_price:
                return RiskAlert(
                    position_id=position_id,
                    token_symbol=token_symbol,
                    alert_type='TAKE_PROFIT',
                    current_price=current_price,
                    trigger_price=take_profit_price,
                    risk_level='LOW',
                    action_required='CLOSE_POSITION',
                    timestamp=datetime.now(timezone.utc),
                    message=f"TAKE PROFIT TRIGGERED: Price ${current_price:.4f} <= ${take_profit_price:.4f}"
                )
            
            # Check critical risk (very close to stop loss)
            if current_price >= critical_risk_price:
                return RiskAlert(
                    position_id=position_id,
                    token_symbol=token_symbol,
                    alert_type='HIGH_RISK',
                    current_price=current_price,
                    trigger_price=critical_risk_price,
                    risk_level='CRITICAL',
                    action_required='MONITOR',
                    timestamp=datetime.now(timezone.utc),
                    message=f"CRITICAL RISK: Price ${current_price:.4f} very close to stop loss"
                )
            
            # Check high risk
            if current_price >= high_risk_price:
                return RiskAlert(
                    position_id=position_id,
                    token_symbol=token_symbol,
                    alert_type='HIGH_RISK',
                    current_price=current_price,
                    trigger_price=high_risk_price,
                    risk_level='HIGH',
                    action_required='MONITOR',
                    timestamp=datetime.now(timezone.utc),
                    message=f"HIGH RISK: Price ${current_price:.4f} approaching stop loss"
                )
            
            return None  # No alerts
            
        except Exception as e:
            logging.error(f"Error evaluating risk conditions: {e}")
            return None
    
    def get_portfolio_risk_summary(self) -> Dict[str, Any]:
        """Get real-time portfolio risk summary"""
        try:
            total_positions = len(self.monitored_positions)
            high_risk_positions = 0
            critical_risk_positions = 0
            total_unrealized_pnl = Decimal('0')
            
            position_details = []
            
            for position_id in self.monitored_positions:
                alert = self.check_position_realtime(position_id)
                position = self.monitored_positions[position_id]
                
                # Count risk levels
                if alert:
                    if alert.risk_level == 'CRITICAL':
                        critical_risk_positions += 1
                    elif alert.risk_level == 'HIGH':
                        high_risk_positions += 1
                
                # Calculate P&L if we have current price
                token_address = position.get('token_address')
                current_price = self.websocket_feed.get_price(token_address)
                
                if current_price:
                    entry_price = Decimal(str(position.get('entry_price_in_usdc', 0)))
                    position_size = Decimal(str(position.get('amount_borrowed', 0)))
                    
                    if entry_price > 0:
                        # For short positions: profit when price goes down
                        pnl = (entry_price - current_price) * position_size / entry_price
                        total_unrealized_pnl += pnl
                        
                        position_details.append({
                            'position_id': position_id,
                            'token_symbol': position.get('token_symbol'),
                            'current_price': float(current_price),
                            'entry_price': float(entry_price),
                            'unrealized_pnl': float(pnl),
                            'risk_level': alert.risk_level if alert else 'LOW'
                        })
            
            return {
                'total_positions': total_positions,
                'high_risk_positions': high_risk_positions,
                'critical_risk_positions': critical_risk_positions,
                'total_unrealized_pnl': float(total_unrealized_pnl),
                'position_details': position_details,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error getting portfolio risk summary: {e}")
            return {}
    
    def start_monitoring(self):
        """Start real-time risk monitoring"""
        if self.running:
            logging.warning("Risk manager already running")
            return
        
        # Load existing open positions
        self.load_open_positions()
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logging.info("🚀 Real-time risk monitoring started")
    
    def stop_monitoring(self):
        """Stop real-time risk monitoring"""
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.websocket_feed.stop()
        logging.info("🛑 Real-time risk monitoring stopped")
    
    def load_open_positions(self):
        """Load existing open positions into monitoring"""
        try:
            open_positions = get_open_positions()
            for position in open_positions:
                self.add_position_to_monitor(position)
            
            logging.info(f"📊 Loaded {len(open_positions)} open positions for monitoring")
            
        except Exception as e:
            logging.error(f"Error loading open positions: {e}")
    
    def _monitoring_loop(self):
        """Main risk monitoring loop"""
        while self.running:
            try:
                for position_id in list(self.monitored_positions.keys()):
                    alert = self.check_position_realtime(position_id)
                    
                    if alert:
                        # Log the alert
                        logging.warning(f"🚨 RISK ALERT: {alert.message}")
                        
                        # Notify all callbacks
                        for callback in self.risk_callbacks:
                            try:
                                callback(alert)
                            except Exception as e:
                                logging.error(f"Error in risk callback: {e}")
                
                # Check every few seconds
                time.sleep(self.check_interval)
                
            except Exception as e:
                logging.error(f"Error in risk monitoring loop: {e}")
                time.sleep(5)

# Global risk manager instance
realtime_risk_manager = RealTimeRiskManager()

def start_realtime_risk_monitoring(risk_callback: Callable[[RiskAlert], None]):
    """Start real-time risk monitoring"""
    realtime_risk_manager.add_risk_callback(risk_callback)
    realtime_risk_manager.start_monitoring()
    return realtime_risk_manager

def stop_realtime_risk_monitoring():
    """Stop real-time risk monitoring"""
    realtime_risk_manager.stop_monitoring()

def add_position_to_realtime_monitoring(position: Dict[str, Any]) -> bool:
    """Add a position to real-time monitoring"""
    return realtime_risk_manager.add_position_to_monitor(position)
