#!/usr/bin/env python3
"""
Test Database Connection
========================

Simple test to verify the database is working correctly.
"""

import os
import sys
import sqlite3
from pathlib import Path

def load_env_file():
    """Load environment variables from .env file"""
    env_path = Path(".env")
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
        print("✅ Loaded .env file")
    else:
        print("❌ .env file not found")

def test_database_connection():
    """Test database connection and operations"""
    print("🧪 Testing Database Connection")
    print("=" * 40)
    
    # Load environment variables
    load_env_file()
    
    # Get DATABASE_URL
    database_url = os.environ.get('DATABASE_URL')
    print(f"DATABASE_URL: {database_url}")
    
    if not database_url:
        print("❌ DATABASE_URL not set")
        return False
    
    # Extract database path for SQLite
    if database_url.startswith('sqlite:///'):
        db_path = database_url.replace('sqlite:///', '')
        print(f"Database file: {db_path}")
        
        if not os.path.exists(db_path):
            print(f"❌ Database file does not exist: {db_path}")
            return False
        
        try:
            # Connect to database
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Test basic operations
            print("\n📋 Testing database operations...")
            
            # List tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   Tables: {tables}")
            
            # Test unlock_events table
            cursor.execute("SELECT COUNT(*) FROM unlock_events")
            unlock_count = cursor.fetchone()[0]
            print(f"   Unlock events: {unlock_count}")
            
            # Test positions table
            cursor.execute("SELECT COUNT(*) FROM positions")
            position_count = cursor.fetchone()[0]
            print(f"   Positions: {position_count}")
            
            # Test inserting a sample position
            cursor.execute('''
                INSERT OR IGNORE INTO positions 
                (position_id, token_symbol, contract_address, amount_borrowed, entry_price_in_usdc, strategy_id)
                VALUES (999, 'TEST', '0x1234567890123456789012345678901234567890', 1000, 10.50, 'test_strategy')
            ''')
            conn.commit()
            
            # Verify insertion
            cursor.execute("SELECT COUNT(*) FROM positions WHERE position_id = 999")
            test_count = cursor.fetchone()[0]
            print(f"   Test position inserted: {test_count > 0}")
            
            # Clean up test data
            cursor.execute("DELETE FROM positions WHERE position_id = 999")
            conn.commit()
            
            conn.close()
            
            print("\n✅ Database connection test PASSED!")
            return True
            
        except Exception as e:
            print(f"❌ Database test failed: {e}")
            return False
    
    else:
        print(f"❌ Unsupported database URL format: {database_url}")
        return False

def test_trading_system_imports():
    """Test importing trading system components"""
    print("\n🧪 Testing Trading System Imports")
    print("=" * 40)
    
    try:
        # Add service paths
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-ledger'))
        
        # Test database handler import
        from db_handler import get_open_positions
        print("✅ db_handler import successful")
        
        # Test getting open positions
        positions = get_open_positions()
        print(f"✅ get_open_positions() returned {len(positions)} positions")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Project Chimera Database Test Suite")
    print("=" * 50)
    
    # Test 1: Database connection
    db_test = test_database_connection()
    
    # Test 2: Trading system imports
    import_test = test_trading_system_imports()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 20)
    print(f"Database Connection: {'✅ PASS' if db_test else '❌ FAIL'}")
    print(f"System Imports: {'✅ PASS' if import_test else '❌ FAIL'}")
    
    if db_test and import_test:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your database is ready for the trading system!")
        
        print("\n🚀 Ready to run:")
        print("   python demo_websocket_trading_integration.py")
        print("   python run_realtime_trading_system.py")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    return db_test and import_test

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
