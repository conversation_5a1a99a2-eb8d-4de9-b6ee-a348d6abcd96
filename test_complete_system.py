#!/usr/bin/env python3
"""
Complete System Test
===================

Final test of the complete WebSocket trading system with proper environment loading.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Load environment variables first
from load_env import load_env_file
load_env_file()

# Add service paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-seer'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-ledger'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-executioner'))

def test_database_connection():
    """Test database connection"""
    print("🗄️ Testing Database Connection")
    print("-" * 30)

    try:
        from universal_db_handler import get_open_positions

        positions = get_open_positions()
        print(f"✅ Database connection successful")
        print(f"   Open positions: {len(positions)}")
        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_websocket_connection():
    """Test WebSocket connection"""
    print("\n📡 Testing WebSocket Connection")
    print("-" * 30)

    try:
        from binance_websocket import BinanceWebSocketPriceFeed
        
        feed = BinanceWebSocketPriceFeed()
        
        # Test subscription
        success = feed.subscribe_to_price('0x1f9840a85d5af5bf1d1762f925bdaddc4201f984')  # UNI
        print(f"✅ WebSocket subscription: {'Success' if success else 'Failed'}")
        
        # Wait for price data
        time.sleep(3)
        
        # Test price fetching
        price = feed.get_price('0x1f9840a85d5af5bf1d1762f925bdaddc4201f984')
        if price:
            print(f"✅ Real-time price: ${float(price):.4f}")
        else:
            print("⚠️ No price data yet (normal for new connections)")
        
        # Test unsubscribe
        unsub_success = feed.unsubscribe_from_price('0x1f9840a85d5af5bf1d1762f925bdaddc4201f984')
        print(f"✅ WebSocket unsubscribe: {'Success' if unsub_success else 'Failed'}")
        
        feed.stop()
        return True
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

def test_strategy_engine():
    """Test real-time strategy engine"""
    print("\n🧠 Testing Strategy Engine")
    print("-" * 30)

    try:
        from realtime_strategy_engine import RealTimeStrategyEngine
        
        engine = RealTimeStrategyEngine()
        
        # Test token data
        demo_token = {
            'token_symbol': 'UNI',
            'contract_address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
            'unlock_date': '2025-01-30',
            'unlock_amount': 1000000,
            'circulating_supply': 750000000,
            'pressure_score': 0.8
        }
        
        # Add token to monitoring
        success = engine.add_token_to_monitor(demo_token)
        print(f"✅ Token monitoring: {'Success' if success else 'Failed'}")
        
        if success:
            time.sleep(3)
            
            # Analyze token
            signal = engine.analyze_token_realtime(demo_token['contract_address'])
            if signal:
                print(f"✅ Signal generated: {signal.signal_type} ({signal.confidence:.1%})")
            else:
                print("⚠️ No signal generated")
        
        engine.stop_monitoring()
        return True
        
    except Exception as e:
        print(f"❌ Strategy engine test failed: {e}")
        return False

def test_risk_manager():
    """Test real-time risk manager"""
    print("\n🛡️ Testing Risk Manager")
    print("-" * 30)

    try:
        from realtime_risk_manager import RealTimeRiskManager
        from decimal import Decimal
        
        manager = RealTimeRiskManager()
        
        # Test position data
        demo_position = {
            'position_id': 999,
            'token_symbol': 'UNI',
            'contract_address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
            'entry_price_in_usdc': '11.00',
            'amount_borrowed': '1000',
            'status': 'OPEN'
        }
        
        # Add position to monitoring
        success = manager.add_position_to_monitor(demo_position)
        print(f"✅ Position monitoring: {'Success' if success else 'Failed'}")
        
        if success:
            time.sleep(3)
            
            # Check position risk
            alert = manager.check_position_realtime(demo_position['position_id'])
            if alert:
                print(f"✅ Risk alert: {alert.alert_type} ({alert.risk_level})")
            else:
                print("✅ No risk alerts (position safe)")
            
            # Get portfolio summary
            summary = manager.get_portfolio_risk_summary()
            if summary:
                print(f"✅ Portfolio summary: {summary.get('total_positions', 0)} positions")
        
        manager.stop_monitoring()
        return True
        
    except Exception as e:
        print(f"❌ Risk manager test failed: {e}")
        return False

def test_execution_engine():
    """Test real-time execution engine"""
    print("\n⚔️ Testing Execution Engine")
    print("-" * 30)

    try:
        from realtime_execution_engine import RealTimeExecutionEngine
        
        engine = RealTimeExecutionEngine()
        
        # Test trade candidate
        demo_candidate = {
            'token_symbol': 'UNI',
            'contract_address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
            'strategy_id': 'test_strategy',
            'pressure_score': 0.85,
            'confidence': 0.9
        }
        
        # Queue execution
        execution_id = engine.queue_execution(demo_candidate, 'OPEN')
        print(f"✅ Execution queued: {'Success' if execution_id else 'Failed'}")
        
        if execution_id:
            time.sleep(3)
            
            # Analyze execution timing
            signal = engine.analyze_execution_timing(execution_id)
            if signal:
                print(f"✅ Execution signal: {signal.signal_type} ({signal.confidence:.1%})")
            else:
                print("⚠️ No execution signal")
            
            # Cancel execution (cleanup)
            engine.cancel_execution(execution_id)
        
        engine.stop_monitoring()
        return True
        
    except Exception as e:
        print(f"❌ Execution engine test failed: {e}")
        return False

def main():
    """Run complete system test"""
    print("🎯 Complete WebSocket Trading System Test")
    print("=" * 50)
    
    # Configure logging to reduce noise
    logging.basicConfig(level=logging.WARNING)
    
    # Run all tests
    tests = [
        ("Database Connection", test_database_connection),
        ("WebSocket Connection", test_websocket_connection),
        ("Strategy Engine", test_strategy_engine),
        ("Risk Manager", test_risk_manager),
        ("Execution Engine", test_execution_engine),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate >= 80:
        print("\n🎉 SYSTEM IS READY FOR PRODUCTION!")
        print("=" * 40)
        print("✅ Database connection working")
        print("✅ WebSocket integration functional")
        print("✅ Real-time strategy engine operational")
        print("✅ Risk management system active")
        print("✅ Execution engine ready")
        
        print("\n🚀 Ready to run:")
        print("   python run_realtime_trading_system.py")
        
        print("\n💰 Your trading system now has:")
        print("   📡 Real-time price monitoring")
        print("   🧠 Dynamic strategy signals")
        print("   🛡️ Instant risk management")
        print("   ⚔️ Optimal trade execution")
        print("   🗄️ Persistent data storage")
        
    else:
        print("\n🔧 SYSTEM NEEDS ATTENTION")
        print("=" * 30)
        print("Some components failed testing.")
        print("Please review the errors above and fix them.")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
