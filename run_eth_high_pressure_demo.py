#!/usr/bin/env python3
"""
ETH High Pressure Trading Demo for Project Chimera
Simulates high-pressure ETH unlock scenarios to demonstrate trading execution
"""

import os
import sys
import time
from decimal import Decimal

# Add service paths
sys.path.append('services/the-oracle')
sys.path.append('services/the-seer')
sys.path.append('services/the-executioner')
sys.path.append('services/the-ledger')
sys.path.append('services/common')

# Set ETH-only configuration
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['TARGET_TOKEN'] = 'ETH'
os.environ['PRESSURE_SCORE_THRESHOLD'] = '0.50'
os.environ['BORROW_AMOUNT_PER_TRADE'] = '10'

def main():
    print("🚀 PROJECT CHIMERA - ETH HIGH PRESSURE DEMO")
    print("=" * 60)
    print("Simulating high-pressure ETH unlock scenarios...")
    
    try:
        from analysis import calculate_unlock_pressure_score
        from paper_trading import PaperTradingEngine
        from price_fetcher import get_realtime_price
        
        # Initialize paper trading engine
        engine = PaperTradingEngine()
        print(f"💰 Initial Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
        
        # Create high-pressure ETH scenarios
        scenarios = [
            {
                'name': 'ETH 2.0 Staking Unlock',
                'unlock_amount': 5000000,  # 5M ETH
                'circulating_supply': 120000000,  # 120M ETH
                'volume_24h': 15000000,  # 15M ETH daily volume
                'description': 'Major ETH 2.0 staking unlock event'
            },
            {
                'name': 'Ethereum Foundation Unlock',
                'unlock_amount': 2000000,  # 2M ETH
                'circulating_supply': 120000000,  # 120M ETH
                'volume_24h': 8000000,   # 8M ETH daily volume
                'description': 'Ethereum Foundation treasury unlock'
            },
            {
                'name': 'Institutional Unlock',
                'unlock_amount': 1000000,  # 1M ETH
                'circulating_supply': 120000000,  # 120M ETH
                'volume_24h': 3000000,   # 3M ETH daily volume
                'description': 'Large institutional ETH unlock'
            }
        ]
        
        print(f"\n🧠 Seer: Analyzing {len(scenarios)} ETH scenarios...")
        
        for i, scenario in enumerate(scenarios):
            print(f"\n{i+1}. {scenario['name']}:")
            print(f"   Description: {scenario['description']}")
            print(f"   Unlock Amount: {scenario['unlock_amount']:,.0f} ETH")
            print(f"   Circulating Supply: {scenario['circulating_supply']:,.0f} ETH")
            print(f"   24h Volume: {scenario['volume_24h']:,.0f} ETH")
            
            # Calculate pressure score manually
            size_impact = scenario['unlock_amount'] / scenario['circulating_supply']
            liquidity_impact = scenario['unlock_amount'] / scenario['volume_24h']
            pressure_score = size_impact * liquidity_impact
            
            print(f"   Size Impact: {size_impact:.4f}")
            print(f"   Liquidity Impact: {liquidity_impact:.4f}")
            print(f"   Pressure Score: {pressure_score:.4f}")
            
            threshold = 0.50
            if pressure_score >= threshold:
                print(f"   🎯 ETH TRADE SIGNAL: HIGH PRESSURE (>= {threshold})")
                
                # Execute paper trade
                print(f"   ⚔️ Executioner: Opening ETH short position...")
                
                # Get current ETH price
                try:
                    weth_address = '******************************************'
                    current_price = get_realtime_price(weth_address)
                    if current_price:
                        print(f"   💰 Current ETH Price: ${current_price}")
                    else:
                        current_price = Decimal('3800')  # Fallback price
                        print(f"   💰 Using fallback ETH Price: ${current_price}")
                except:
                    current_price = Decimal('3800')
                    print(f"   💰 Using fallback ETH Price: ${current_price}")
                
                borrow_amount = 10  # 10 ETH
                
                success, message, received_usdc = engine.simulate_borrow_and_swap(
                    weth_address, borrow_amount
                )
                
                if success:
                    print(f"   ✅ SUCCESS: Borrowed {borrow_amount} ETH")
                    print(f"   💵 Received: {received_usdc} USDC")
                    print(f"   📊 Updated Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
                    
                    # Show position details
                    portfolio = engine.get_portfolio_summary()
                    if portfolio['open_positions']:
                        pos = portfolio['open_positions'][-1]  # Latest position
                        print(f"   📈 Position: {pos['amount_borrowed']} ETH @ ${pos['entry_price_in_usdc']}")
                else:
                    print(f"   ❌ FAILED: {message}")
                    
            else:
                print(f"   ⏸️ ETH SIGNAL: MONITOR (< {threshold})")
        
        # Show final portfolio
        portfolio = engine.get_portfolio_summary()
        print(f"\n📊 Final Portfolio Summary:")
        print(f"   ETH Balance: {portfolio['balances']['eth']}")
        print(f"   USDC Balance: {portfolio['balances']['usdc']}")
        print(f"   Open Positions: {portfolio['positions']['open']}")
        print(f"   Total P&L: ${portfolio['performance']['total_pnl_usd']}")
        
        if portfolio['open_positions']:
            print(f"\n📈 Active ETH Positions:")
            for pos in portfolio['open_positions']:
                print(f"   • {pos['token_symbol']}: {pos['amount_borrowed']} tokens @ ${pos['entry_price_in_usdc']}")
        
        print(f"\n🎉 ETH high-pressure demo complete!")
        print(f"🚀 System ready for live ETH trading!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
