#!/usr/bin/env python3
"""
Binance WebSocket Live Demo
==========================

Simple demonstration of real-time price streaming using Binance WebSocket.
This shows how to use the WebSocket for live trading applications.
"""

import sys
import os
import time
import logging
from decimal import Decimal
from typing import Dict

# Add services to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-ledger'))

def demo_realtime_prices():
    """Demonstrate real-time price streaming"""
    print("🚀 Binance WebSocket Live Price Demo")
    print("=" * 50)
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed
        
        # Create WebSocket feed
        feed = BinanceWebSocketPriceFeed()
        
        # Demo tokens for live streaming
        demo_tokens = [
            'ETH',  # Ethereum
            'BTC',  # Bitcoin
            '******************************************',  # UNI
            '******************************************',  # AAVE
        ]
        
        print(f"📡 Starting real-time price feed for {len(demo_tokens)} tokens...")
        print("   This will show live price updates from Binance WebSocket")
        print("   Press Ctrl+C to stop\n")
        
        # Subscribe to all tokens
        for token in demo_tokens:
            success = feed.subscribe_to_price(token)
            symbol = feed.get_binance_symbol(token)
            print(f"   {'✅' if success else '❌'} {symbol}: {'Subscribed' if success else 'Failed'}")
        
        print("\n⏳ Waiting for WebSocket connection...")
        time.sleep(3)
        
        print("\n💰 Live Price Stream (updating every 2 seconds):")
        print("-" * 60)
        
        # Store previous prices for change calculation
        previous_prices = {}
        
        try:
            while True:
                current_prices = {}
                
                for token in demo_tokens:
                    price = feed.get_price(token)
                    symbol = feed.get_binance_symbol(token)
                    
                    if price:
                        current_price = float(price)
                        current_prices[token] = current_price
                        
                        # Calculate price change
                        if token in previous_prices:
                            change = current_price - previous_prices[token]
                            change_pct = (change / previous_prices[token]) * 100
                            
                            # Choose arrow based on change
                            if change > 0:
                                arrow = "📈"
                                color = "+"
                            elif change < 0:
                                arrow = "📉"
                                color = ""
                            else:
                                arrow = "➡️"
                                color = " "
                            
                            print(f"   {arrow} {symbol:8} ${current_price:8.4f} ({color}{change_pct:+.2f}%)")
                        else:
                            print(f"   💰 {symbol:8} ${current_price:8.4f} (initial)")
                    else:
                        print(f"   ❌ {symbol:8} No price available")
                
                previous_prices = current_prices.copy()
                
                # Update every 2 seconds
                time.sleep(2)
                
        except KeyboardInterrupt:
            print("\n\n🛑 Stopping live price feed...")
            feed.stop()
            print("✅ WebSocket stopped successfully")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False
    
    return True

def demo_price_callbacks():
    """Demonstrate price callbacks for automated trading"""
    print("\n🔔 Price Callback Demo")
    print("=" * 30)
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed
        
        feed = BinanceWebSocketPriceFeed()
        
        # Price alert thresholds
        eth_alert_price = 3900.0  # Alert if ETH goes above $3900
        
        def price_alert_callback(symbol: str, price: Decimal):
            """Callback function for price alerts"""
            price_float = float(price)
            
            if symbol == 'ETHUSDT' and price_float > eth_alert_price:
                print(f"🚨 PRICE ALERT: ETH is now ${price_float:.2f} (above ${eth_alert_price})")
            elif symbol == 'BTCUSDT':
                print(f"📊 BTC Update: ${price_float:.2f}")
        
        print("📡 Setting up price alerts...")
        print(f"   🚨 ETH Alert: Will notify if price > ${eth_alert_price}")
        print("   📊 BTC: Will show all updates")
        
        # Subscribe with callback
        feed.subscribe_to_price('ETH', callback=price_alert_callback)
        feed.subscribe_to_price('BTC', callback=price_alert_callback)
        
        print("\n⏳ Monitoring for 30 seconds...")
        time.sleep(30)
        
        feed.stop()
        print("✅ Callback demo completed")
        
    except Exception as e:
        print(f"❌ Callback demo failed: {e}")

def demo_trading_integration():
    """Show how WebSocket integrates with trading system"""
    print("\n🎯 Trading Integration Demo")
    print("=" * 35)
    
    try:
        from binance_websocket import get_realtime_price_binance
        
        # Simulate trading decision based on real-time prices
        tokens_to_monitor = ['ETH', 'BTC', '******************************************']
        
        print("💼 Simulating trading decisions based on real-time prices...")
        
        for token in tokens_to_monitor:
            price = get_realtime_price_binance(token)
            
            if price:
                price_float = float(price)
                
                # Simulate trading logic
                if token == 'ETH':
                    if price_float > 3900:
                        decision = "🔴 SELL Signal (High price)"
                    elif price_float < 3800:
                        decision = "🟢 BUY Signal (Low price)"
                    else:
                        decision = "🟡 HOLD (Neutral)"
                elif token == 'BTC':
                    if price_float > 120000:
                        decision = "🔴 SELL Signal (High price)"
                    elif price_float < 115000:
                        decision = "🟢 BUY Signal (Low price)"
                    else:
                        decision = "🟡 HOLD (Neutral)"
                else:
                    decision = "📊 Monitor only"
                
                print(f"   💰 {token}: ${price_float:.4f} → {decision}")
            else:
                print(f"   ❌ {token}: No price data")
        
        print("\n✅ Trading integration demo completed")
        
    except Exception as e:
        print(f"❌ Trading demo failed: {e}")

def main():
    """Run all WebSocket demos"""
    print("🎉 Binance WebSocket Live Demonstration")
    print("=" * 50)
    print("This demo shows real-time price streaming capabilities")
    print("for your ETH trading system.\n")
    
    # Reduce logging noise
    logging.basicConfig(level=logging.ERROR)
    
    try:
        # Demo 1: Real-time price streaming
        demo_realtime_prices()
        
        # Demo 2: Price callbacks
        demo_price_callbacks()
        
        # Demo 3: Trading integration
        demo_trading_integration()
        
        print("\n🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("\n🚀 Your Binance WebSocket integration is ready for:")
        print("   ✅ Real-time price streaming")
        print("   ✅ Automated price alerts")
        print("   ✅ Trading system integration")
        print("   ✅ Live market monitoring")
        
        print("\n🎯 Next Steps:")
        print("   1. Integrate WebSocket with your trading strategies")
        print("   2. Set up automated alerts for key price levels")
        print("   3. Use real-time data for risk management")
        print("   4. Deploy to production with live trading")
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
