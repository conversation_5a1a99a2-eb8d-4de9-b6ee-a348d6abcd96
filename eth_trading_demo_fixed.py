#!/usr/bin/env python3
"""
ETH Trading Demo - Fixed Version
Shows successful ETH trading execution
"""

import os
import sys
from decimal import Decimal

sys.path.append('services/the-executioner')
sys.path.append('services/the-ledger')
sys.path.append('services/common')

os.environ['PAPER_TRADING_MODE'] = 'true'

def main():
    print("🚀 PROJECT CHIMERA - ETH TRADING DEMO (FIXED)")
    print("=" * 60)
    print("Demonstrating successful ETH trading execution...")
    
    try:
        from paper_trading import PaperTradingEngine
        from price_fetcher import get_realtime_price
        
        # Initialize paper trading engine
        engine = PaperTradingEngine()
        print(f"💰 Initial Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
        
        # ETH extreme scenario
        scenario = {
            'name': 'ETH Massive Unlock',
            'unlock_amount': 50000000,  # 50M ETH
            'circulating_supply': 120000000,  # 120M ETH
            'volume_24h': 5000000,   # 5M ETH daily volume
            'description': 'Massive ETH unlock during low liquidity'
        }
        
        print(f"\n🧠 Seer: Analyzing ETH scenario...")
        print(f"   Scenario: {scenario['name']}")
        print(f"   Description: {scenario['description']}")
        print(f"   Unlock Amount: {scenario['unlock_amount']:,.0f} ETH")
        print(f"   Circulating Supply: {scenario['circulating_supply']:,.0f} ETH")
        print(f"   24h Volume: {scenario['volume_24h']:,.0f} ETH")
        
        # Calculate pressure score
        size_impact = scenario['unlock_amount'] / scenario['circulating_supply']
        liquidity_impact = scenario['unlock_amount'] / scenario['volume_24h']
        pressure_score = size_impact * liquidity_impact
        
        print(f"   Size Impact: {size_impact:.4f}")
        print(f"   Liquidity Impact: {liquidity_impact:.4f}")
        print(f"   Pressure Score: {pressure_score:.4f}")
        
        threshold = 0.50
        if pressure_score >= threshold:
            print(f"   🎯 ETH TRADE SIGNAL: EXTREME PRESSURE (>= {threshold})")
            
            # Get current ETH price
            try:
                weth_address = '******************************************'
                current_price = get_realtime_price(weth_address)
                if current_price:
                    print(f"   💰 Current ETH Price: ${current_price}")
                else:
                    current_price = Decimal('3800')
                    print(f"   💰 Using fallback ETH Price: ${current_price}")
            except:
                current_price = Decimal('3800')
                print(f"   💰 Using fallback ETH Price: ${current_price}")
            
            print(f"   ⚔️ Executioner: Opening ETH short position...")
            
            # Create ETH candidate for trading
            eth_candidate = {
                'token_symbol': 'ETH',
                'contract_address': weth_address,
                'unlock_amount': scenario['unlock_amount'],
                'unlock_date': '2024-12-01T00:00:00Z',
                'pressure_score': pressure_score
            }
            
            try:
                # Execute the trade
                position = engine.simulate_position_entry(eth_candidate)
                print(f"   ✅ SUCCESS: Opened ETH position")
                print(f"   💵 Entry Price: ${position['entry_price_in_usdc']:.2f}")
                print(f"   📊 Amount Shorted: {position['amount_shorted']} ETH")
                print(f"   📊 Position ID: {position['position_id']}")
                
                # Show risk management
                print(f"   🛡️ Ledger: Risk management active...")
                entry_price = Decimal(str(position['entry_price_in_usdc']))
                stop_loss_price = entry_price * Decimal('1.15')
                take_profit_price = entry_price * Decimal('0.90')
                print(f"   🚨 Stop-Loss: ${stop_loss_price:.2f} (15% protection)")
                print(f"   🎯 Take-Profit: ${take_profit_price:.2f} (10% target)")
                
            except Exception as e:
                print(f"   ❌ FAILED: {e}")
                
        else:
            print(f"   ⏸️ ETH SIGNAL: MONITOR (< {threshold})")
        
        # Show final portfolio
        portfolio = engine.get_portfolio_summary()
        print(f"\n📊 Final Portfolio Summary:")
        print(f"   ETH Balance: {portfolio['balances']['eth']}")
        print(f"   USDC Balance: {portfolio['balances']['usdc']:,.2f}")
        print(f"   Open Positions: {portfolio['positions']['open']}")
        print(f"   Total P&L: ${portfolio['performance']['total_pnl_usd']}")
        
        if portfolio['open_positions']:
            print(f"\n📈 Active ETH Positions:")
            for pos in portfolio['open_positions']:
                amount = pos.get('amount_shorted', 'Unknown')
                print(f"   • {pos['token_symbol']}: {amount} tokens @ ${pos['entry_price_in_usdc']:.2f}")
        
        print(f"\n🎉 ETH trading demo complete!")
        print(f"🚀 System successfully executed ETH trading!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
