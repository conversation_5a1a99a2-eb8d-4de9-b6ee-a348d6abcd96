#!/usr/bin/env python3
"""
Simple Database Creation Script
==============================

Creates a working SQLite database for the trading system.
"""

import os
import sqlite3
from pathlib import Path

def create_database():
    """Create SQLite database with all required tables"""
    
    # Remove existing database if it exists
    db_path = "./chimera_trading.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️ Removed existing database: {db_path}")
    
    print(f"🗄️ Creating new SQLite database: {db_path}")
    
    # Connect to SQLite database (creates if doesn't exist)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create unlock_events table
    cursor.execute('''
        CREATE TABLE unlock_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token_symbol VARCHAR(20) NOT NULL,
            contract_address VARCHAR(42) NOT NULL,
            unlock_date TIMESTAMP NOT NULL,
            unlock_amount DECIMAL NOT NULL,
            circulating_supply DECIMAL,
            total_supply DECIMAL,
            pressure_score DECIMAL,
            volume_24h DECIMAL,
            market_cap DECIMAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(contract_address, unlock_date)
        )
    ''')
    print("✅ Created unlock_events table")
    
    # Create positions table
    cursor.execute('''
        CREATE TABLE positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_id INTEGER UNIQUE,
            token_symbol VARCHAR(20) NOT NULL,
            contract_address VARCHAR(42) NOT NULL,
            strategy_id VARCHAR(50),
            amount_borrowed DECIMAL NOT NULL,
            entry_price_in_usdc DECIMAL NOT NULL,
            stop_loss_price DECIMAL,
            take_profit_price DECIMAL,
            status VARCHAR(20) DEFAULT 'OPEN',
            borrow_tx_hash VARCHAR(66),
            swap_tx_hash VARCHAR(66),
            close_tx_hash VARCHAR(66),
            opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            closed_at TIMESTAMP,
            pnl_usd DECIMAL,
            close_reason TEXT,
            pressure_score DECIMAL,
            confidence DECIMAL
        )
    ''')
    print("✅ Created positions table")
    
    # Create trade_history table
    cursor.execute('''
        CREATE TABLE trade_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_id INTEGER,
            action VARCHAR(20) NOT NULL,
            token_symbol VARCHAR(20),
            amount DECIMAL,
            price DECIMAL,
            tx_hash VARCHAR(66),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            gas_used INTEGER,
            gas_price DECIMAL
        )
    ''')
    print("✅ Created trade_history table")
    
    # Create risk_events table
    cursor.execute('''
        CREATE TABLE risk_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_id INTEGER,
            event_type VARCHAR(30) NOT NULL,
            risk_level VARCHAR(20),
            current_price DECIMAL,
            trigger_price DECIMAL,
            message TEXT,
            action_taken VARCHAR(50),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    print("✅ Created risk_events table")
    
    # Create strategy_signals table
    cursor.execute('''
        CREATE TABLE strategy_signals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token_symbol VARCHAR(20) NOT NULL,
            contract_address VARCHAR(42) NOT NULL,
            signal_type VARCHAR(30) NOT NULL,
            pressure_score DECIMAL,
            current_price DECIMAL,
            confidence DECIMAL,
            reason TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            executed BOOLEAN DEFAULT FALSE
        )
    ''')
    print("✅ Created strategy_signals table")
    
    # Create indexes
    cursor.execute('CREATE INDEX idx_unlock_events_date ON unlock_events(unlock_date)')
    cursor.execute('CREATE INDEX idx_unlock_events_symbol ON unlock_events(token_symbol)')
    cursor.execute('CREATE INDEX idx_positions_status ON positions(status)')
    cursor.execute('CREATE INDEX idx_positions_symbol ON positions(token_symbol)')
    print("✅ Created indexes")
    
    # Insert sample data
    sample_events = [
        ('UNI', '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984', '2025-01-30 12:00:00', 1000000, 750000000, 1000000000, 0.8, 50000000, 8000000000),
        ('AAVE', '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9', '2025-02-01 15:00:00', 500000, 14000000, 16000000, 0.9, 30000000, 4500000000),
        ('COMP', '0xc00e94cb662c3520282e6f5717214004a7f26888', '2025-01-31 10:00:00', 100000, 10000000, 10000000, 0.7, 20000000, 500000000)
    ]
    
    cursor.executemany('''
        INSERT INTO unlock_events 
        (token_symbol, contract_address, unlock_date, unlock_amount, circulating_supply, total_supply, pressure_score, volume_24h, market_cap)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', sample_events)
    print("✅ Inserted sample unlock events")
    
    # Commit changes
    conn.commit()
    
    # Test the database
    cursor.execute("SELECT COUNT(*) FROM unlock_events")
    count = cursor.fetchone()[0]
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    print(f"\n🎉 Database created successfully!")
    print(f"   Database file: {os.path.abspath(db_path)}")
    print(f"   Tables created: {tables}")
    print(f"   Sample events: {count}")
    
    # Close connection
    conn.close()
    
    return db_path

def update_env_file(db_path):
    """Update .env file with correct DATABASE_URL"""
    env_path = ".env"
    
    if os.path.exists(env_path):
        # Read current .env file
        with open(env_path, 'r') as f:
            lines = f.readlines()
        
        # Update DATABASE_URL line
        updated_lines = []
        database_url_updated = False
        
        for line in lines:
            if line.startswith('DATABASE_URL='):
                updated_lines.append(f'DATABASE_URL=sqlite:///{db_path}\n')
                database_url_updated = True
            else:
                updated_lines.append(line)
        
        # Add DATABASE_URL if it wasn't found
        if not database_url_updated:
            updated_lines.append(f'DATABASE_URL=sqlite:///{db_path}\n')
        
        # Write updated .env file
        with open(env_path, 'w') as f:
            f.writelines(updated_lines)
        
        print(f"✅ Updated {env_path} with DATABASE_URL")
    else:
        # Create new .env file
        with open(env_path, 'w') as f:
            f.write(f'DATABASE_URL=sqlite:///{db_path}\n')
        print(f"✅ Created {env_path} with DATABASE_URL")

def main():
    """Main function"""
    print("🗄️ Project Chimera Database Setup")
    print("=" * 40)
    
    # Create database
    db_path = create_database()
    
    # Update environment file
    update_env_file(db_path)
    
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 40)
    print("Your trading system database is ready!")
    print("\nNext steps:")
    print("1. Run: python demo_websocket_trading_integration.py")
    print("2. Run: python run_realtime_trading_system.py")
    print("\nDatabase info:")
    print(f"   File: {os.path.abspath(db_path)}")
    print(f"   URL: sqlite:///{db_path}")

if __name__ == "__main__":
    main()
