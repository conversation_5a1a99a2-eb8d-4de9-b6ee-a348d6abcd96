#!/usr/bin/env python3
"""
Real-time Trade Execution Engine with Binance WebSocket Integration
==================================================================

Enhanced trade execution system that uses Binance WebSocket for optimal
entry and exit timing, real-time slippage monitoring, and dynamic execution.

Features:
- Real-time price monitoring for optimal execution timing
- Dynamic slippage protection
- Market condition analysis for execution decisions
- WebSocket-powered entry and exit optimization
- Advanced execution analytics
"""

import os
import sys
import json
import time
import logging
import threading
from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass

# Add ledger path for WebSocket access
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'the-ledger'))

from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
from paper_trading import execute_paper_trade, close_paper_position

@dataclass
class ExecutionSignal:
    """Real-time execution signal"""
    token_symbol: str
    contract_address: str
    signal_type: str  # 'EXECUTE_NOW', 'WAIT_FOR_DIP', 'ABORT', 'CLOSE_NOW'
    current_price: Decimal
    target_price: Optional[Decimal]
    confidence: float
    slippage_risk: str  # 'LOW', 'MEDIUM', 'HIGH'
    market_condition: str  # 'FAVORABLE', 'NEUTRAL', 'UNFAVORABLE'
    timestamp: datetime
    reason: str

class RealTimeExecutionEngine:
    """Enhanced execution engine with WebSocket integration"""
    
    def __init__(self):
        self.websocket_feed = BinanceWebSocketPriceFeed()
        self.pending_executions = {}   # execution_id -> execution_data
        self.execution_callbacks = []  # List of callback functions
        self.price_history = {}        # token_address -> price_history
        self.running = False
        self.monitoring_thread = None
        
        # Execution parameters
        self.max_slippage_pct = Decimal("0.005")  # 0.5% max slippage
        self.execution_timeout = 300  # 5 minutes max wait for optimal price
        self.price_improvement_threshold = Decimal("0.002")  # 0.2% improvement target
        
        # Market condition thresholds
        self.volatility_threshold = 0.02  # 2% volatility = high risk
        self.volume_spike_threshold = 2.0  # 2x normal volume
        
        logging.info("⚔️ Real-time Execution Engine initialized")
    
    def add_execution_callback(self, callback: Callable[[ExecutionSignal], None]):
        """Add a callback function for execution signals"""
        self.execution_callbacks.append(callback)
        logging.info(f"📞 Added execution callback: {callback.__name__}")
    
    def queue_execution(self, candidate: Dict[str, Any], execution_type: str = 'OPEN') -> str:
        """Queue a trade for optimal execution timing"""
        try:
            execution_id = f"{execution_type}_{candidate.get('token_symbol')}_{int(time.time())}"
            contract_address = candidate.get('contract_address')
            token_symbol = candidate.get('token_symbol')
            
            # Subscribe to WebSocket for this token
            success = self.websocket_feed.subscribe_to_price(contract_address)
            if not success:
                logging.error(f"Failed to subscribe to {token_symbol} WebSocket")
                return None
            
            execution_data = {
                'execution_id': execution_id,
                'candidate': candidate,
                'execution_type': execution_type,
                'queued_time': time.time(),
                'target_price': None,
                'status': 'QUEUED'
            }
            
            self.pending_executions[execution_id] = execution_data
            self.price_history[contract_address] = []
            
            logging.info(f"📋 Queued {execution_type} execution for {token_symbol} (ID: {execution_id})")
            return execution_id
            
        except Exception as e:
            logging.error(f"Error queuing execution: {e}")
            return None
    
    def cancel_execution(self, execution_id: str):
        """Cancel a pending execution"""
        try:
            if execution_id in self.pending_executions:
                execution_data = self.pending_executions[execution_id]
                contract_address = execution_data['candidate'].get('contract_address')
                token_symbol = execution_data['candidate'].get('token_symbol')
                
                # Unsubscribe from WebSocket
                self.websocket_feed.unsubscribe_from_price(contract_address)
                
                del self.pending_executions[execution_id]
                if contract_address in self.price_history:
                    del self.price_history[contract_address]
                
                logging.info(f"❌ Cancelled execution {execution_id} for {token_symbol}")
                
        except Exception as e:
            logging.error(f"Error cancelling execution: {e}")
    
    def analyze_execution_timing(self, execution_id: str) -> Optional[ExecutionSignal]:
        """Analyze optimal execution timing for a queued trade"""
        try:
            if execution_id not in self.pending_executions:
                return None
            
            execution_data = self.pending_executions[execution_id]
            candidate = execution_data['candidate']
            contract_address = candidate.get('contract_address')
            token_symbol = candidate.get('token_symbol')
            execution_type = execution_data['execution_type']
            
            # Get current price from WebSocket
            current_price = self.websocket_feed.get_price(contract_address)
            if not current_price:
                current_price = get_realtime_price_binance(contract_address)
                if not current_price:
                    return None
            
            # Update price history
            self.update_price_history(contract_address, current_price)
            
            # Analyze market conditions
            market_condition = self.analyze_market_conditions(contract_address)
            slippage_risk = self.calculate_slippage_risk(contract_address)
            
            # Determine execution signal
            signal_type, confidence, reason = self.determine_execution_signal(
                execution_data, current_price, market_condition, slippage_risk
            )
            
            # Create execution signal
            signal = ExecutionSignal(
                token_symbol=token_symbol,
                contract_address=contract_address,
                signal_type=signal_type,
                current_price=current_price,
                target_price=execution_data.get('target_price'),
                confidence=confidence,
                slippage_risk=slippage_risk,
                market_condition=market_condition,
                timestamp=datetime.now(timezone.utc),
                reason=reason
            )
            
            return signal
            
        except Exception as e:
            logging.error(f"Error analyzing execution timing: {e}")
            return None
    
    def update_price_history(self, contract_address: str, price: Decimal):
        """Update price history for market analysis"""
        if contract_address not in self.price_history:
            self.price_history[contract_address] = []
        
        price_entry = {
            'price': float(price),
            'timestamp': time.time()
        }
        
        self.price_history[contract_address].append(price_entry)
        
        # Keep only last 10 minutes of data
        cutoff_time = time.time() - 600
        self.price_history[contract_address] = [
            p for p in self.price_history[contract_address] 
            if p['timestamp'] > cutoff_time
        ]
    
    def analyze_market_conditions(self, contract_address: str) -> str:
        """Analyze current market conditions"""
        try:
            price_history = self.price_history.get(contract_address, [])
            if len(price_history) < 10:
                return "NEUTRAL"
            
            # Calculate volatility (standard deviation of returns)
            prices = [p['price'] for p in price_history[-20:]]
            if len(prices) < 2:
                return "NEUTRAL"
            
            returns = []
            for i in range(1, len(prices)):
                ret = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(ret)
            
            if not returns:
                return "NEUTRAL"
            
            # Calculate volatility
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = variance ** 0.5
            
            # Determine market condition
            if volatility > self.volatility_threshold:
                return "UNFAVORABLE"  # High volatility = unfavorable for execution
            elif volatility < self.volatility_threshold * 0.5:
                return "FAVORABLE"    # Low volatility = favorable
            else:
                return "NEUTRAL"
                
        except Exception as e:
            logging.error(f"Error analyzing market conditions: {e}")
            return "NEUTRAL"
    
    def calculate_slippage_risk(self, contract_address: str) -> str:
        """Calculate slippage risk based on recent price movements"""
        try:
            price_history = self.price_history.get(contract_address, [])
            if len(price_history) < 5:
                return "MEDIUM"
            
            # Look at recent price changes
            recent_prices = [p['price'] for p in price_history[-5:]]
            max_change = 0
            
            for i in range(1, len(recent_prices)):
                change = abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                max_change = max(max_change, change)
            
            # Determine slippage risk
            if max_change > 0.01:  # >1% recent change
                return "HIGH"
            elif max_change > 0.005:  # >0.5% recent change
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            logging.error(f"Error calculating slippage risk: {e}")
            return "MEDIUM"
    
    def determine_execution_signal(self, execution_data: Dict, current_price: Decimal,
                                 market_condition: str, slippage_risk: str) -> Tuple[str, float, str]:
        """Determine execution signal based on market analysis"""
        try:
            execution_type = execution_data['execution_type']
            queued_time = execution_data['queued_time']
            time_elapsed = time.time() - queued_time
            
            # Check timeout
            if time_elapsed > self.execution_timeout:
                return "EXECUTE_NOW", 0.6, "Execution timeout reached"
            
            # For OPEN positions (short trades)
            if execution_type == 'OPEN':
                # Favorable conditions for short entry
                if market_condition == "FAVORABLE" and slippage_risk == "LOW":
                    return "EXECUTE_NOW", 0.9, "Optimal market conditions for entry"
                
                # Wait for better conditions if market is unfavorable
                if market_condition == "UNFAVORABLE" or slippage_risk == "HIGH":
                    if time_elapsed < self.execution_timeout * 0.7:
                        return "WAIT_FOR_DIP", 0.4, "Waiting for better market conditions"
                    else:
                        return "EXECUTE_NOW", 0.6, "Time running out, executing despite conditions"
                
                # Neutral conditions - execute
                return "EXECUTE_NOW", 0.7, "Neutral market conditions, proceeding"
            
            # For CLOSE positions
            elif execution_type == 'CLOSE':
                # Always execute close orders quickly to manage risk
                if slippage_risk == "HIGH":
                    return "EXECUTE_NOW", 0.8, "High slippage risk but must close position"
                else:
                    return "EXECUTE_NOW", 0.9, "Closing position with good conditions"
            
            return "EXECUTE_NOW", 0.5, "Default execution"
            
        except Exception as e:
            logging.error(f"Error determining execution signal: {e}")
            return "EXECUTE_NOW", 0.3, f"Error in analysis: {str(e)}"
    
    def execute_trade_now(self, execution_id: str) -> Dict[str, Any]:
        """Execute a trade immediately"""
        try:
            if execution_id not in self.pending_executions:
                raise Exception(f"Execution {execution_id} not found")
            
            execution_data = self.pending_executions[execution_id]
            candidate = execution_data['candidate']
            execution_type = execution_data['execution_type']
            token_symbol = candidate.get('token_symbol')
            
            logging.warning(f"⚔️ EXECUTING {execution_type}: {token_symbol}")
            
            # Get final execution price
            contract_address = candidate.get('contract_address')
            execution_price = self.websocket_feed.get_price(contract_address)
            if not execution_price:
                execution_price = get_realtime_price_binance(contract_address)
            
            # Execute based on type
            if execution_type == 'OPEN':
                result = execute_paper_trade(candidate)
                result['execution_price'] = float(execution_price) if execution_price else None
                result['execution_id'] = execution_id
                
            elif execution_type == 'CLOSE':
                position_id = candidate.get('position_id')
                close_reason = candidate.get('close_reason', 'Manual close')
                result = close_paper_position(position_id, close_reason, execution_price)
                result['execution_price'] = float(execution_price) if execution_price else None
                result['execution_id'] = execution_id
            
            else:
                raise Exception(f"Unknown execution type: {execution_type}")
            
            # Clean up
            self.cancel_execution(execution_id)
            
            logging.info(f"✅ Executed {execution_type} for {token_symbol}: {result}")
            return result
            
        except Exception as e:
            logging.error(f"Error executing trade: {e}")
            return {'success': False, 'error': str(e), 'execution_id': execution_id}
    
    def start_monitoring(self):
        """Start real-time execution monitoring"""
        if self.running:
            logging.warning("Execution engine already running")
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logging.info("🚀 Real-time execution monitoring started")
    
    def stop_monitoring(self):
        """Stop real-time execution monitoring"""
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.websocket_feed.stop()
        logging.info("🛑 Real-time execution monitoring stopped")
    
    def _monitoring_loop(self):
        """Main execution monitoring loop"""
        while self.running:
            try:
                for execution_id in list(self.pending_executions.keys()):
                    signal = self.analyze_execution_timing(execution_id)
                    
                    if signal:
                        # Notify callbacks
                        for callback in self.execution_callbacks:
                            try:
                                callback(signal)
                            except Exception as e:
                                logging.error(f"Error in execution callback: {e}")
                        
                        # Auto-execute if signal says so
                        if signal.signal_type == "EXECUTE_NOW" and signal.confidence > 0.7:
                            self.execute_trade_now(execution_id)
                
                # Check every 5 seconds
                time.sleep(5)
                
            except Exception as e:
                logging.error(f"Error in execution monitoring loop: {e}")
                time.sleep(5)

# Global execution engine instance
realtime_execution_engine = RealTimeExecutionEngine()

def queue_realtime_execution(candidate: Dict[str, Any], execution_type: str = 'OPEN') -> str:
    """Queue a trade for optimal execution timing"""
    return realtime_execution_engine.queue_execution(candidate, execution_type)

def execute_immediately(execution_id: str) -> Dict[str, Any]:
    """Execute a queued trade immediately"""
    return realtime_execution_engine.execute_trade_now(execution_id)

def start_realtime_execution_monitoring(execution_callback: Callable[[ExecutionSignal], None]):
    """Start real-time execution monitoring"""
    realtime_execution_engine.add_execution_callback(execution_callback)
    realtime_execution_engine.start_monitoring()
    return realtime_execution_engine

def stop_realtime_execution_monitoring():
    """Stop real-time execution monitoring"""
    realtime_execution_engine.stop_monitoring()
