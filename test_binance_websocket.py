#!/usr/bin/env python3
"""
Enhanced Binance WebSocket Test Suite
====================================

Comprehensive test of Binance WebSocket integration with improved error handling
and real-time price streaming capabilities for Project Chimera.
"""

import os
import sys
import time
import logging
from pathlib import Path
from decimal import Decimal

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def test_binance_websocket():
    """Test Binance WebSocket price feed functionality"""
    print("🧪 Testing Binance WebSocket Price Feed")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
        
        # Test tokens (major DeFi tokens with confirmed Binance listings)
        test_tokens = [
            {
                'address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
                'symbol': 'UNI',
                'binance_symbol': 'UNIUSDT'
            },
            {
                'address': '0xc00e94cb662c3520282e6f5717214004a7f26888',
                'symbol': 'COMP',
                'binance_symbol': 'COMPUSDT'
            },
            {
                'address': '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9',
                'symbol': 'AAVE',
                'binance_symbol': 'AAVEUSDT'
            }
        ]
        
        print(f"📊 Testing {len(test_tokens)} tokens:")
        for token in test_tokens:
            print(f"   🪙 {token['symbol']} ({token['binance_symbol']})")
        
        # Test 1: Individual price fetching
        print("\n🔍 Test 1: Individual Price Fetching")
        print("-" * 30)
        
        for token in test_tokens:
            print(f"\n📈 Testing {token['symbol']}...")
            
            try:
                price = get_realtime_price_binance(token['address'])
                if price:
                    print(f"   ✅ Price: ${price:.4f}")
                else:
                    print(f"   ❌ No price available")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Test 2: WebSocket feed initialization
        print("\n🔗 Test 2: WebSocket Feed Initialization")
        print("-" * 40)
        
        feed = BinanceWebSocketPriceFeed(use_testnet=False)
        
        # Subscribe to all test tokens
        print("📡 Subscribing to price feeds...")
        for token in test_tokens:
            success = feed.subscribe_to_price(token['address'])
            print(f"   {'✅' if success else '❌'} {token['symbol']}: {'Subscribed' if success else 'Failed'}")
        
        # Wait for initial data
        print("\n⏱️ Waiting for initial price data...")
        time.sleep(5)
        
        # Test 3: Real-time price monitoring
        print("\n📊 Test 3: Real-time Price Monitoring")
        print("-" * 35)
        
        for i in range(3):  # Monitor for 3 iterations
            print(f"\n📈 Price Check #{i+1}:")
            
            for token in test_tokens:
                try:
                    price = feed.get_price(token['address'])
                    if price:
                        print(f"   💰 {token['symbol']}: ${price:.4f}")
                    else:
                        print(f"   ⚠️ {token['symbol']}: No price data")
                except Exception as e:
                    print(f"   ❌ {token['symbol']}: Error - {e}")
            
            if i < 2:  # Don't wait after last iteration
                time.sleep(3)
        
        # Test 4: Feed status and statistics
        print("\n📋 Test 4: Feed Status")
        print("-" * 20)
        
        status = feed.get_status()
        print(f"   🔗 Connected: {status['connected']}")
        print(f"   🏃 Running: {status['running']}")
        print(f"   📡 Subscribed symbols: {len(status['subscribed_symbols'])}")
        print(f"   💾 Active prices: {status['active_prices']}")
        print(f"   🔄 Reconnect attempts: {status['reconnect_attempts']}")
        
        if status['latest_prices']:
            print("   💰 Latest prices:")
            for symbol, price in status['latest_prices'].items():
                print(f"      {symbol}: {price}")
        
        # Test 5: Error handling
        print("\n🚨 Test 5: Error Handling")
        print("-" * 25)
        
        # Test with invalid token address
        invalid_address = '0x0000000000000000000000000000000000000000'
        print(f"🔍 Testing invalid address: {invalid_address}")
        
        price = get_realtime_price_binance(invalid_address)
        if price is None:
            print("   ✅ Correctly handled invalid address")
        else:
            print(f"   ⚠️ Unexpected price for invalid address: ${price:.4f}")
        
        # Clean up
        print("\n🛑 Stopping price feed...")
        feed.stop()
        time.sleep(2)
        
        print("\n" + "=" * 50)
        print("✅ Binance WebSocket Test Completed Successfully!")
        print("🎯 Ready for integration with live paper trading")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure websocket-client is installed: pip install websocket-client")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_price_fetcher():
    """Test integration with the main price_fetcher module"""
    print("\n🔗 Testing Integration with Price Fetcher")
    print("=" * 45)
    
    try:
        from price_fetcher import get_realtime_price
        
        test_tokens = [
            '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',  # UNI
            '0xc00e94cb662c3520282e6f5717214004a7f26888',  # COMP
        ]
        
        print("📊 Testing enhanced price fetcher (Binance + CoinGecko)...")
        
        for token in test_tokens:
            print(f"\n🔍 Testing {token}...")
            
            try:
                price = get_realtime_price(token)
                if price:
                    print(f"   ✅ Price: ${price:.4f}")
                else:
                    print(f"   ⚠️ No price available")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print("\n✅ Integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Binance WebSocket Integration Test Suite")
    print("🎯 Project Chimera - Real-time Price Feed Validation")
    print("=" * 60)
    
    # Check dependencies
    try:
        import websocket
        print("✅ websocket-client library available")
    except ImportError:
        print("❌ websocket-client not installed")
        print("📦 Install with: pip install websocket-client")
        return False
    
    try:
        import requests
        print("✅ requests library available")
    except ImportError:
        print("❌ requests not installed")
        print("📦 Install with: pip install requests")
        return False
    
    # Run tests
    tests = [
        ("Binance WebSocket", test_binance_websocket),
        ("Price Fetcher Integration", test_integration_with_price_fetcher)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 ALL TESTS PASSED! Binance WebSocket integration is ready!")
        print("🚀 Ready for live paper trading with real-time price feeds")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
