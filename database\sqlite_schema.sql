-- SQLite Schema for Project Chimera Trading System
-- Compatible with SQLite 3.x

-- Unlock Events Table
CREATE TABLE IF NOT EXISTS unlock_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol VARCHAR(20) NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    unlock_date TIMESTAMP NOT NULL,
    unlock_amount DECIMAL NOT NULL,
    circulating_supply DECIMAL,
    total_supply DECIMAL,
    pressure_score DECIMAL,
    volume_24h DECIMAL,
    market_cap DECIMAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(contract_address, unlock_date)
);

-- Positions Table
CREATE TABLE IF NOT EXISTS positions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    position_id INTEGER UNIQUE,
    token_symbol VARCHAR(20) NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    strategy_id VARCHAR(50),
    amount_borrowed DECIMAL NOT NULL,
    entry_price_in_usdc DECIMAL NOT NULL,
    stop_loss_price DECIMAL,
    take_profit_price DECIMAL,
    status VARCHAR(20) DEFAULT 'OPEN',
    borrow_tx_hash VARCHAR(66),
    swap_tx_hash VARCHAR(66),
    close_tx_hash VARCHAR(66),
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP,
    pnl_usd DECIMAL,
    close_reason TEXT,
    pressure_score DECIMAL,
    confidence DECIMAL,
    CHECK (status IN ('OPEN', 'CLOSING', 'CLOSED', 'FAILED'))
);

-- Trade History Table
CREATE TABLE IF NOT EXISTS trade_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    position_id INTEGER,
    action VARCHAR(20) NOT NULL,
    token_symbol VARCHAR(20),
    amount DECIMAL,
    price DECIMAL,
    tx_hash VARCHAR(66),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    gas_used INTEGER,
    gas_price DECIMAL,
    FOREIGN KEY (position_id) REFERENCES positions(position_id)
);

-- Risk Events Table
CREATE TABLE IF NOT EXISTS risk_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    position_id INTEGER,
    event_type VARCHAR(30) NOT NULL,
    risk_level VARCHAR(20),
    current_price DECIMAL,
    trigger_price DECIMAL,
    message TEXT,
    action_taken VARCHAR(50),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (position_id) REFERENCES positions(position_id)
);

-- Strategy Signals Table
CREATE TABLE IF NOT EXISTS strategy_signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_symbol VARCHAR(20) NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    signal_type VARCHAR(30) NOT NULL,
    pressure_score DECIMAL,
    current_price DECIMAL,
    confidence DECIMAL,
    reason TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    executed BOOLEAN DEFAULT FALSE
);

-- Performance Metrics Table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    total_trades INTEGER DEFAULT 0,
    successful_trades INTEGER DEFAULT 0,
    total_pnl DECIMAL DEFAULT 0,
    total_volume DECIMAL DEFAULT 0,
    avg_hold_time_hours DECIMAL,
    max_drawdown DECIMAL,
    sharpe_ratio DECIMAL,
    win_rate DECIMAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date)
);

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_unlock_events_date ON unlock_events(unlock_date);
CREATE INDEX IF NOT EXISTS idx_unlock_events_symbol ON unlock_events(token_symbol);
CREATE INDEX IF NOT EXISTS idx_unlock_events_address ON unlock_events(contract_address);

CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(token_symbol);
CREATE INDEX IF NOT EXISTS idx_positions_opened ON positions(opened_at);

CREATE INDEX IF NOT EXISTS idx_trade_history_position ON trade_history(position_id);
CREATE INDEX IF NOT EXISTS idx_trade_history_timestamp ON trade_history(timestamp);

CREATE INDEX IF NOT EXISTS idx_risk_events_position ON risk_events(position_id);
CREATE INDEX IF NOT EXISTS idx_risk_events_timestamp ON risk_events(timestamp);

CREATE INDEX IF NOT EXISTS idx_strategy_signals_symbol ON strategy_signals(token_symbol);
CREATE INDEX IF NOT EXISTS idx_strategy_signals_timestamp ON strategy_signals(timestamp);

-- Create Views for Easy Querying
CREATE VIEW IF NOT EXISTS open_positions AS
SELECT 
    p.*,
    ROUND((julianday('now') - julianday(p.opened_at)) * 24, 2) as hours_open
FROM positions p 
WHERE p.status = 'OPEN';

CREATE VIEW IF NOT EXISTS recent_signals AS
SELECT 
    s.*,
    ROUND((julianday('now') - julianday(s.timestamp)) * 24, 2) as hours_ago
FROM strategy_signals s 
WHERE s.timestamp > datetime('now', '-24 hours')
ORDER BY s.timestamp DESC;

CREATE VIEW IF NOT EXISTS position_summary AS
SELECT 
    token_symbol,
    COUNT(*) as total_positions,
    SUM(CASE WHEN status = 'OPEN' THEN 1 ELSE 0 END) as open_positions,
    SUM(CASE WHEN status = 'CLOSED' THEN 1 ELSE 0 END) as closed_positions,
    AVG(pnl_usd) as avg_pnl,
    SUM(pnl_usd) as total_pnl
FROM positions 
GROUP BY token_symbol;
