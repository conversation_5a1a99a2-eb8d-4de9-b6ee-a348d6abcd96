#!/usr/bin/env python3
"""
Universal Database Handler
==========================

Database handler that works with both SQLite and PostgreSQL.
Automatically detects the database type from DATABASE_URL.
"""

import os
import logging
import sqlite3
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from urllib.parse import urlparse

class UniversalDBHandler:
    """Universal database handler for SQLite and PostgreSQL"""
    
    def __init__(self):
        self.db_url = os.environ.get("DATABASE_URL")
        if not self.db_url:
            raise Exception("DATABASE_URL environment variable not set")
        
        # Parse database URL
        parsed = urlparse(self.db_url)
        self.db_type = parsed.scheme
        
        if self.db_type == 'sqlite':
            self.db_path = self.db_url.replace('sqlite:///', '')
        elif self.db_type == 'postgresql':
            self.pg_config = {
                'host': parsed.hostname,
                'port': parsed.port,
                'database': parsed.path.lstrip('/'),
                'user': parsed.username,
                'password': parsed.password
            }
        else:
            raise Exception(f"Unsupported database type: {self.db_type}")
        
        logging.info(f"Database handler initialized for {self.db_type}")
    
    def get_connection(self):
        """Get database connection based on type"""
        if self.db_type == 'sqlite':
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            return conn
        elif self.db_type == 'postgresql':
            import psycopg2
            import psycopg2.extras
            return psycopg2.connect(self.db_url)
        else:
            raise Exception(f"Unsupported database type: {self.db_type}")
    
    def execute_query(self, query: str, params: tuple = None, fetch: str = 'all') -> List[Dict]:
        """Execute query and return results"""
        conn = self.get_connection()
        try:
            if self.db_type == 'sqlite':
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if fetch == 'all':
                    results = cursor.fetchall()
                    return [dict(row) for row in results]
                elif fetch == 'one':
                    result = cursor.fetchone()
                    return dict(result) if result else None
                else:
                    return []
                    
            elif self.db_type == 'postgresql':
                import psycopg2.extras
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    
                    if fetch == 'all':
                        results = cursor.fetchall()
                        return [dict(row) for row in results]
                    elif fetch == 'one':
                        result = cursor.fetchone()
                        return dict(result) if result else None
                    else:
                        return []
        finally:
            conn.close()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute update/insert query and return affected rows"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()

# Global database handler instance
db_handler = UniversalDBHandler()

def get_open_positions() -> List[Dict[str, Any]]:
    """Retrieves all open trading positions"""
    try:
        # Adjust query based on database type
        if db_handler.db_type == 'sqlite':
            query = """
                SELECT id as position_id, token_symbol, contract_address, 
                       amount_borrowed, entry_price_in_usdc, status,
                       borrow_tx_hash, swap_tx_hash, opened_at as created_at
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY opened_at ASC
            """
        else:  # PostgreSQL
            query = """
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """
        
        results = db_handler.execute_query(query)
        logging.info(f"Retrieved {len(results)} open positions")
        return results
        
    except Exception as e:
        logging.error(f"Error querying open positions: {e}")
        return []

def update_position_status(position_id: int, status: str, close_tx_hash: str = None):
    """Update position status"""
    try:
        if db_handler.db_type == 'sqlite':
            if status == 'CLOSED':
                query = """
                    UPDATE positions 
                    SET status = ?, close_tx_hash = ?, closed_at = datetime('now')
                    WHERE id = ?
                """
                params = (status, close_tx_hash, position_id)
            else:
                query = "UPDATE positions SET status = ? WHERE id = ?"
                params = (status, position_id)
        else:  # PostgreSQL
            if status == 'CLOSED':
                query = """
                    UPDATE positions 
                    SET status = %s, close_tx_hash = %s, closed_at = NOW()
                    WHERE id = %s
                """
                params = (status, close_tx_hash, position_id)
            else:
                query = "UPDATE positions SET status = %s WHERE id = %s"
                params = (status, position_id)
        
        rows_affected = db_handler.execute_update(query, params)
        logging.info(f"Updated position {position_id} status to {status}")
        return rows_affected > 0
        
    except Exception as e:
        logging.error(f"Error updating position status: {e}")
        return False

def insert_position(position_data: Dict[str, Any]) -> Optional[int]:
    """Insert a new position and return position_id"""
    try:
        if db_handler.db_type == 'sqlite':
            query = """
                INSERT INTO positions 
                (position_id, token_symbol, contract_address, strategy_id, amount_borrowed, 
                 entry_price_in_usdc, stop_loss_price, take_profit_price, status, 
                 borrow_tx_hash, swap_tx_hash, pressure_score, confidence)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                position_data.get('position_id'),
                position_data.get('token_symbol'),
                position_data.get('contract_address'),
                position_data.get('strategy_id'),
                position_data.get('amount_borrowed'),
                position_data.get('entry_price_in_usdc'),
                position_data.get('stop_loss_price'),
                position_data.get('take_profit_price'),
                position_data.get('status', 'OPEN'),
                position_data.get('borrow_tx_hash'),
                position_data.get('swap_tx_hash'),
                position_data.get('pressure_score'),
                position_data.get('confidence')
            )
        else:  # PostgreSQL
            query = """
                INSERT INTO positions 
                (token_symbol, token_address, strategy_id, amount_shorted, 
                 entry_price_in_usdc, unlock_date, status, borrow_tx_hash, swap_tx_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            params = (
                position_data.get('token_symbol'),
                position_data.get('token_address'),
                position_data.get('strategy_id'),
                position_data.get('amount_shorted'),
                position_data.get('entry_price_in_usdc'),
                position_data.get('unlock_date'),
                position_data.get('status', 'OPEN'),
                position_data.get('borrow_tx_hash'),
                position_data.get('swap_tx_hash')
            )
        
        if db_handler.db_type == 'sqlite':
            db_handler.execute_update(query, params)
            # Get the last inserted row id
            result = db_handler.execute_query("SELECT last_insert_rowid() as id", fetch='one')
            return result['id'] if result else None
        else:
            result = db_handler.execute_query(query, params, fetch='one')
            return result['id'] if result else None
            
    except Exception as e:
        logging.error(f"Error inserting position: {e}")
        return None

def get_position_by_id(position_id: int) -> Optional[Dict[str, Any]]:
    """Get position by ID"""
    try:
        if db_handler.db_type == 'sqlite':
            query = "SELECT * FROM positions WHERE id = ?"
        else:
            query = "SELECT * FROM positions WHERE id = %s"
        
        result = db_handler.execute_query(query, (position_id,), fetch='one')
        return result
        
    except Exception as e:
        logging.error(f"Error getting position {position_id}: {e}")
        return None

def insert_trade_history(trade_data: Dict[str, Any]) -> bool:
    """Insert trade history record"""
    try:
        if db_handler.db_type == 'sqlite':
            query = """
                INSERT INTO trade_history 
                (position_id, action, token_symbol, amount, price, tx_hash, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
        else:
            query = """
                INSERT INTO trade_history 
                (position_id, action, token_symbol, amount, price, tx_hash, notes)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
        
        params = (
            trade_data.get('position_id'),
            trade_data.get('action'),
            trade_data.get('token_symbol'),
            trade_data.get('amount'),
            trade_data.get('price'),
            trade_data.get('tx_hash'),
            trade_data.get('notes')
        )
        
        rows_affected = db_handler.execute_update(query, params)
        return rows_affected > 0
        
    except Exception as e:
        logging.error(f"Error inserting trade history: {e}")
        return False

def insert_risk_event(risk_data: Dict[str, Any]) -> bool:
    """Insert risk event record"""
    try:
        if db_handler.db_type == 'sqlite':
            query = """
                INSERT INTO risk_events 
                (position_id, event_type, risk_level, current_price, trigger_price, message, action_taken)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
        else:
            query = """
                INSERT INTO risk_events 
                (position_id, event_type, risk_level, current_price, trigger_price, message, action_taken)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
        
        params = (
            risk_data.get('position_id'),
            risk_data.get('event_type'),
            risk_data.get('risk_level'),
            risk_data.get('current_price'),
            risk_data.get('trigger_price'),
            risk_data.get('message'),
            risk_data.get('action_taken')
        )
        
        rows_affected = db_handler.execute_update(query, params)
        return rows_affected > 0
        
    except Exception as e:
        logging.error(f"Error inserting risk event: {e}")
        return False
