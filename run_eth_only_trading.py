#!/usr/bin/env python3
"""
ETH-Only Trading System for Project Chimera
Focuses exclusively on Ethereum unlock events and trading opportunities
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

# Add service paths
sys.path.append('services/the-oracle')
sys.path.append('services/the-seer')
sys.path.append('services/the-executioner')
sys.path.append('services/the-ledger')
sys.path.append('services/common')

# Set ETH-only configuration
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['TARGET_TOKEN'] = 'ETH'
os.environ['FOCUS_MODE'] = 'ETH_ONLY'
os.environ['PRESSURE_SCORE_THRESHOLD'] = '0.50'  # Lower threshold for ETH
os.environ['BORROW_AMOUNT_PER_TRADE'] = '10'     # 10 ETH per trade

def main():
    print("🚀 PROJECT CHIMERA - ETHEREUM ONLY MODE")
    print("=" * 60)
    
    try:
        # Import services
        from data_sources import fetch_token_unlocks_data
        from analysis import calculate_unlock_pressure_score
        from paper_trading import PaperTradingEngine
        
        print("🔮 Oracle: Fetching ETH unlock data...")
        all_events = fetch_token_unlocks_data()
        
        # Filter for ETH-related events
        eth_events = []
        for event in all_events:
            token_symbol = event.get('token_symbol', '').upper()
            if token_symbol in ['ETH', 'ETHEREUM', 'WETH']:
                eth_events.append(event)
        
        print(f"📊 Found {len(eth_events)} ETH-specific events")
        
        if not eth_events:
            # Create ETH market analysis
            print("📈 No direct ETH unlocks found. Analyzing ETH market conditions...")
            eth_event = {
                'token_symbol': 'ETH',
                'contract_address': '******************************************',  # WETH
                'unlock_amount': 1000000,  # 1M ETH
                'unlock_date': '2024-12-01T00:00:00Z'
            }
            eth_events = [eth_event]
        
        print("\n🧠 Seer: Analyzing ETH opportunities...")
        
        # Initialize paper trading engine
        engine = PaperTradingEngine()
        print(f"💰 Initial Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
        
        for i, event in enumerate(eth_events[:3]):  # Analyze top 3
            print(f"\n{i+1}. ETH Analysis:")
            print(f"   Token: {event.get('token_symbol', 'ETH')}")
            print(f"   Unlock Amount: {event.get('unlock_amount', 0):,.0f} tokens")
            print(f"   Unlock Date: {event.get('unlock_date', 'Unknown')}")
            
            try:
                pressure_score = calculate_unlock_pressure_score(event)
                print(f"   Pressure Score: {pressure_score:.4f}")
                
                threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.50'))
                
                if pressure_score >= threshold:
                    print(f"   🎯 ETH TRADE SIGNAL: HIGH PRESSURE (>= {threshold})")
                    
                    # Execute paper trade
                    print(f"   ⚔️ Executioner: Opening ETH short position...")
                    
                    # For ETH, we'll use WETH contract address
                    weth_address = '******************************************'
                    borrow_amount = float(os.environ.get('BORROW_AMOUNT_PER_TRADE', '10'))
                    
                    success, message, received_usdc = engine.simulate_borrow_and_swap(
                        weth_address, borrow_amount
                    )
                    
                    if success:
                        print(f"   ✅ SUCCESS: Received {received_usdc} USDC")
                        print(f"   📊 Updated Portfolio: {engine.paper_wallet_balance} ETH, {engine.paper_usdc_balance} USDC")
                    else:
                        print(f"   ❌ FAILED: {message}")
                        
                else:
                    print(f"   ⏸️ ETH SIGNAL: MONITOR (< {threshold})")
                    
            except Exception as e:
                print(f"   ❌ Analysis failed: {e}")
        
        # Show final portfolio
        portfolio = engine.get_portfolio_summary()
        print(f"\n📊 Final Portfolio Summary:")
        print(f"   ETH Balance: {portfolio['balances']['eth']}")
        print(f"   USDC Balance: {portfolio['balances']['usdc']}")
        print(f"   Open Positions: {portfolio['positions']['open']}")
        print(f"   Total P&L: ${portfolio['performance']['total_pnl_usd']}")
        
        if portfolio['open_positions']:
            print(f"\n📈 Active ETH Positions:")
            for pos in portfolio['open_positions']:
                print(f"   • {pos['token_symbol']}: {pos['amount_borrowed']} tokens @ ${pos['entry_price_in_usdc']}")
        
        print(f"\n🎉 ETH-only trading session complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
