#!/usr/bin/env python3
"""
Real-time Trading System with Binance WebSocket Integration
==========================================================

Comprehensive real-time trading system that integrates all components:
- Real-time strategy engine with WebSocket price monitoring
- Live risk management with instant alerts
- Optimal trade execution timing
- Continuous position monitoring

This is the main orchestrator for the enhanced WebSocket-powered trading system.
"""

import os
import sys
import json
import time
import logging
import signal
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List

# Add service paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-seer'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-ledger'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'the-executioner'))

from realtime_strategy_engine import (
    RealTimeSignal, start_realtime_monitoring, stop_realtime_monitoring
)
from realtime_risk_manager import (
    RiskAlert, start_realtime_risk_monitoring, stop_realtime_risk_monitoring,
    add_position_to_realtime_monitoring
)
from realtime_execution_engine import (
    ExecutionSignal, queue_realtime_execution, execute_immediately,
    start_realtime_execution_monitoring, stop_realtime_execution_monitoring
)

class RealTimeTradingSystem:
    """Comprehensive real-time trading system orchestrator"""
    
    def __init__(self):
        self.running = False
        self.strategy_engine = None
        self.risk_manager = None
        self.execution_engine = None
        
        # Trading state
        self.active_signals = {}      # token_symbol -> latest_signal
        self.pending_executions = {}  # execution_id -> execution_data
        self.active_positions = {}    # position_id -> position_data
        
        # Performance tracking
        self.signals_generated = 0
        self.trades_executed = 0
        self.risk_alerts_triggered = 0
        self.start_time = None
        
        logging.info("🎯 Real-time Trading System initialized")
    
    def handle_trading_signal(self, signal: RealTimeSignal):
        """Handle real-time trading signals from strategy engine"""
        try:
            self.signals_generated += 1
            self.active_signals[signal.token_symbol] = signal
            
            logging.warning(f"📊 TRADING SIGNAL: {signal.token_symbol}")
            logging.info(f"   Type: {signal.signal_type}")
            logging.info(f"   Price: ${signal.current_price:.4f}")
            logging.info(f"   Confidence: {signal.confidence:.1%}")
            logging.info(f"   Reason: {signal.reason}")
            
            # Handle different signal types
            if signal.signal_type == "STRONG_SHORT" and signal.confidence > 0.8:
                self.execute_strong_short_signal(signal)
            elif signal.signal_type == "WEAK_SHORT" and signal.confidence > 0.6:
                self.execute_weak_short_signal(signal)
            elif signal.signal_type == "HOLD":
                logging.info(f"   Action: Monitoring {signal.token_symbol}")
            else:
                logging.info(f"   Action: No action for {signal.signal_type}")
                
        except Exception as e:
            logging.error(f"Error handling trading signal: {e}")
    
    def execute_strong_short_signal(self, signal: RealTimeSignal):
        """Execute strong short signals immediately"""
        try:
            logging.warning(f"🎯 EXECUTING STRONG SHORT: {signal.token_symbol}")
            
            # Create trade candidate
            candidate = {
                'token_symbol': signal.token_symbol,
                'contract_address': signal.contract_address,
                'strategy_id': 'realtime_websocket_v1',
                'pressure_score': signal.pressure_score,
                'entry_reason': f"Strong short signal: {signal.reason}",
                'confidence': signal.confidence,
                'signal_timestamp': signal.timestamp.isoformat()
            }
            
            # Queue for optimal execution
            execution_id = queue_realtime_execution(candidate, 'OPEN')
            if execution_id:
                self.pending_executions[execution_id] = {
                    'candidate': candidate,
                    'signal': signal,
                    'queued_time': time.time()
                }
                logging.info(f"   Queued execution: {execution_id}")
            
        except Exception as e:
            logging.error(f"Error executing strong short signal: {e}")
    
    def execute_weak_short_signal(self, signal: RealTimeSignal):
        """Execute weak short signals with more caution"""
        try:
            logging.info(f"🔍 EVALUATING WEAK SHORT: {signal.token_symbol}")
            
            # For weak signals, wait for better confirmation
            # Could implement additional filters here
            if signal.confidence > 0.7:
                self.execute_strong_short_signal(signal)  # Treat as strong
            else:
                logging.info(f"   Confidence too low ({signal.confidence:.1%}), monitoring...")
                
        except Exception as e:
            logging.error(f"Error executing weak short signal: {e}")
    
    def handle_risk_alert(self, alert: RiskAlert):
        """Handle real-time risk alerts"""
        try:
            self.risk_alerts_triggered += 1
            
            logging.warning(f"🚨 RISK ALERT: {alert.token_symbol}")
            logging.warning(f"   Type: {alert.alert_type}")
            logging.warning(f"   Risk Level: {alert.risk_level}")
            logging.warning(f"   Price: ${alert.current_price:.4f}")
            logging.warning(f"   Action: {alert.action_required}")
            logging.warning(f"   Message: {alert.message}")
            
            # Handle different alert types
            if alert.action_required == "CLOSE_POSITION":
                self.execute_position_close(alert)
            elif alert.risk_level == "CRITICAL":
                self.handle_critical_risk(alert)
            elif alert.risk_level == "HIGH":
                self.handle_high_risk(alert)
                
        except Exception as e:
            logging.error(f"Error handling risk alert: {e}")
    
    def execute_position_close(self, alert: RiskAlert):
        """Execute immediate position close"""
        try:
            logging.warning(f"🔴 CLOSING POSITION: {alert.token_symbol} (ID: {alert.position_id})")
            
            # Create close candidate
            close_candidate = {
                'position_id': alert.position_id,
                'token_symbol': alert.token_symbol,
                'close_reason': alert.message,
                'alert_type': alert.alert_type,
                'risk_level': alert.risk_level
            }
            
            # Queue for immediate execution
            execution_id = queue_realtime_execution(close_candidate, 'CLOSE')
            if execution_id:
                # Execute immediately for risk management
                result = execute_immediately(execution_id)
                logging.warning(f"   Position closed: {result}")
                
                # Remove from active positions
                if alert.position_id in self.active_positions:
                    del self.active_positions[alert.position_id]
            
        except Exception as e:
            logging.error(f"Error closing position: {e}")
    
    def handle_critical_risk(self, alert: RiskAlert):
        """Handle critical risk situations"""
        try:
            logging.error(f"💀 CRITICAL RISK: {alert.token_symbol}")
            
            # Could implement emergency procedures here
            # For now, just log and monitor closely
            
        except Exception as e:
            logging.error(f"Error handling critical risk: {e}")
    
    def handle_high_risk(self, alert: RiskAlert):
        """Handle high risk situations"""
        try:
            logging.warning(f"⚠️ HIGH RISK: {alert.token_symbol}")
            
            # Could implement risk reduction strategies here
            # For now, just monitor
            
        except Exception as e:
            logging.error(f"Error handling high risk: {e}")
    
    def handle_execution_signal(self, signal: ExecutionSignal):
        """Handle execution timing signals"""
        try:
            logging.info(f"⚔️ EXECUTION SIGNAL: {signal.token_symbol}")
            logging.info(f"   Signal: {signal.signal_type}")
            logging.info(f"   Price: ${signal.current_price:.4f}")
            logging.info(f"   Confidence: {signal.confidence:.1%}")
            logging.info(f"   Market: {signal.market_condition}")
            logging.info(f"   Slippage Risk: {signal.slippage_risk}")
            logging.info(f"   Reason: {signal.reason}")
            
            # Execution signals are handled automatically by the execution engine
            # This is just for logging and monitoring
            
        except Exception as e:
            logging.error(f"Error handling execution signal: {e}")
    
    def load_tokens_to_monitor(self) -> List[Dict[str, Any]]:
        """Load tokens to monitor from recent unlock events"""
        try:
            # For demo, use some popular tokens
            # In production, this would load from the Oracle's recent data
            demo_tokens = [
                {
                    'token_symbol': 'UNI',
                    'contract_address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
                    'unlock_date': '2025-01-30',
                    'unlock_amount': 1000000,
                    'circulating_supply': 750000000,
                    'pressure_score': 0.8
                },
                {
                    'token_symbol': 'AAVE',
                    'contract_address': '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9',
                    'unlock_date': '2025-02-01',
                    'unlock_amount': 500000,
                    'circulating_supply': 14000000,
                    'pressure_score': 0.9
                },
                {
                    'token_symbol': 'COMP',
                    'contract_address': '0xc00e94cb662c3520282e6f5717214004a7f26888',
                    'unlock_date': '2025-01-31',
                    'unlock_amount': 100000,
                    'circulating_supply': 10000000,
                    'pressure_score': 0.7
                }
            ]
            
            logging.info(f"📋 Loaded {len(demo_tokens)} tokens for monitoring")
            return demo_tokens
            
        except Exception as e:
            logging.error(f"Error loading tokens: {e}")
            return []
    
    def start_system(self):
        """Start the complete real-time trading system"""
        try:
            if self.running:
                logging.warning("System already running")
                return
            
            self.running = True
            self.start_time = datetime.now(timezone.utc)
            
            logging.warning("🚀 STARTING REAL-TIME TRADING SYSTEM")
            logging.info("=" * 60)
            
            # Load tokens to monitor
            tokens = self.load_tokens_to_monitor()
            if not tokens:
                logging.error("No tokens to monitor")
                return
            
            # Start strategy engine
            logging.info("🧠 Starting real-time strategy engine...")
            self.strategy_engine = start_realtime_monitoring(tokens, self.handle_trading_signal)
            
            # Start risk manager
            logging.info("🛡️ Starting real-time risk manager...")
            self.risk_manager = start_realtime_risk_monitoring(self.handle_risk_alert)
            
            # Start execution engine
            logging.info("⚔️ Starting real-time execution engine...")
            self.execution_engine = start_realtime_execution_monitoring(self.handle_execution_signal)
            
            logging.warning("✅ REAL-TIME TRADING SYSTEM STARTED")
            logging.info(f"   Monitoring {len(tokens)} tokens")
            logging.info(f"   WebSocket connections active")
            logging.info(f"   Real-time risk management enabled")
            logging.info(f"   Optimal execution timing enabled")
            logging.info("=" * 60)
            
        except Exception as e:
            logging.error(f"Error starting system: {e}")
            self.stop_system()
    
    def stop_system(self):
        """Stop the real-time trading system"""
        try:
            if not self.running:
                return
            
            logging.warning("🛑 STOPPING REAL-TIME TRADING SYSTEM")
            
            # Stop all engines
            if self.strategy_engine:
                stop_realtime_monitoring()
            if self.risk_manager:
                stop_realtime_risk_monitoring()
            if self.execution_engine:
                stop_realtime_execution_monitoring()
            
            self.running = False
            
            # Print final statistics
            self.print_session_summary()
            
            logging.warning("✅ REAL-TIME TRADING SYSTEM STOPPED")
            
        except Exception as e:
            logging.error(f"Error stopping system: {e}")
    
    def print_session_summary(self):
        """Print session performance summary"""
        try:
            if not self.start_time:
                return
            
            duration = datetime.now(timezone.utc) - self.start_time
            
            logging.info("📊 SESSION SUMMARY")
            logging.info("=" * 40)
            logging.info(f"Duration: {duration}")
            logging.info(f"Signals Generated: {self.signals_generated}")
            logging.info(f"Trades Executed: {self.trades_executed}")
            logging.info(f"Risk Alerts: {self.risk_alerts_triggered}")
            logging.info(f"Active Positions: {len(self.active_positions)}")
            logging.info("=" * 40)
            
        except Exception as e:
            logging.error(f"Error printing summary: {e}")
    
    def run_forever(self):
        """Run the system until interrupted"""
        try:
            self.start_system()
            
            # Set up signal handlers for graceful shutdown
            def signal_handler(signum, frame):
                logging.info("Received interrupt signal, shutting down...")
                self.stop_system()
                sys.exit(0)
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # Keep running
            while self.running:
                time.sleep(10)
                
                # Print periodic status
                if self.signals_generated > 0:
                    logging.info(f"💫 Status: {self.signals_generated} signals, "
                               f"{self.trades_executed} trades, "
                               f"{self.risk_alerts_triggered} alerts")
                
        except KeyboardInterrupt:
            logging.info("Keyboard interrupt received")
            self.stop_system()
        except Exception as e:
            logging.error(f"Error in main loop: {e}")
            self.stop_system()

def main():
    """Main entry point"""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('realtime_trading.log')
        ]
    )
    
    # Create and run system
    system = RealTimeTradingSystem()
    
    print("🎉 Real-time Trading System with Binance WebSocket")
    print("=" * 60)
    print("Features:")
    print("  ✅ Real-time price monitoring via Binance WebSocket")
    print("  ✅ Dynamic strategy signals with confidence scoring")
    print("  ✅ Instant risk management and stop-loss execution")
    print("  ✅ Optimal trade execution timing")
    print("  ✅ Continuous position monitoring")
    print("=" * 60)
    print("Press Ctrl+C to stop")
    print()
    
    system.run_forever()

if __name__ == "__main__":
    main()
