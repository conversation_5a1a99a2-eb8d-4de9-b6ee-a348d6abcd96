#!/usr/bin/env python3
"""
Binance WebSocket Price Feed for Project Chimera
Provides real-time price data with low latency and high reliability
"""

import json
import logging
import threading
import time
import websocket
from decimal import Decimal
from typing import Dict, Optional, Callable, Set
from collections import defaultdict

class BinanceWebSocketPriceFeed:
    """
    Real-time price feed using Binance WebSocket API
    Supports multiple symbols with automatic reconnection
    Uses official Binance WebSocket endpoints for maximum reliability
    """

    def __init__(self, use_testnet=False):
        self.ws = None
        self.prices = {}  # symbol -> price
        self.last_update = {}  # symbol -> timestamp
        self.subscribed_symbols = set()
        self.callbacks = defaultdict(list)  # symbol -> [callback_functions]
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds

        # WebSocket endpoints (production vs testnet)
        if use_testnet:
            self.ws_base_url = "wss://fstream.binancefuture.com"
            self.rest_base_url = "https://testnet.binancefuture.com"
            logging.info("🧪 Using Binance TESTNET endpoints")
        else:
            self.ws_base_url = "wss://stream.binance.com:9443"
            self.rest_base_url = "https://api.binance.com"
            logging.info("🌐 Using Binance PRODUCTION endpoints")
        
        # Enhanced token symbol mappings (contract address -> Binance symbol)
        # Using official Binance spot trading pairs with comprehensive coverage
        self.token_mappings = {
            # Native ETH and wrapped ETH
            'ETH': 'ETHUSDT',                                           # Native ETH
            'ETHEREUM': 'ETHUSDT',                                      # Alternative ETH reference
            '******************************************': 'ETHUSDT',   # WETH (Wrapped ETH)

            # Major DeFi tokens with high trading volume
            '******************************************': 'UNIUSDT',   # UNI
            '******************************************': 'AAVEUSDT',  # AAVE
            '******************************************': 'COMPUSDT',  # COMP
            '******************************************': 'LINKUSDT',  # LINK
            '******************************************': 'BTCUSDT',   # WBTC (use BTC price)
            '******************************************': 'DAIUSDT',   # DAI
            '******************************************': 'USDCUSDT', # USDC
            '******************************************': 'USDTUSDT',  # USDT

            # Additional major tokens
            '******************************************': 'SHIBUSDT', # SHIB
            '******************************************': 'BUSDUSDT', # BUSD
            '******************************************': 'OKBUSDT',  # OKB
            '******************************************': 'ETHUSDT',   # ETH (zero address)

            # Major DeFi tokens (verified Binance symbols)
            '******************************************': 'UNIUSDT',   # UNI
            '******************************************': 'COMPUSDT',  # COMP
            '******************************************': 'AAVEUSDT',  # AAVE
            '******************************************': 'LINKUSDT',  # LINK
            '******************************************': 'DAIUSDT',   # DAI
            '******************************************': 'CRVUSDT',   # CRV
            '******************************************': 'MKRUSDT',   # MKR
            '******************************************': 'YFIUSDT',   # YFI
            '******************************************': 'REPUSDT',   # REP

            # Stablecoins
            '******************************************': 'USDTUSDT',  # USDT
            '******************************************': 'USDCUSDT',  # USDC

            # Major cryptocurrencies
            'BTC': 'BTCUSDT',                                           # Bitcoin
            'BITCOIN': 'BTCUSDT',                                       # Alternative BTC reference
            # Add more verified pairs as needed
        }

        # Rate limiting to respect Binance API limits
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
        logging.info("🔗 Binance WebSocket Price Feed initialized")
    
    def get_binance_symbol(self, token_address: str) -> Optional[str]:
        """Convert token contract address or symbol to Binance trading symbol"""
        # Handle direct symbol lookups (ETH, BTC, etc.)
        if token_address.upper() in ['ETH', 'ETHEREUM']:
            return 'ETHUSDT'
        elif token_address.upper() in ['BTC', 'BITCOIN']:
            return 'BTCUSDT'

        # Handle contract address lookups
        return self.token_mappings.get(token_address.lower())
    
    def add_token_mapping(self, token_address: str, binance_symbol: str):
        """Add a new token mapping"""
        self.token_mappings[token_address.lower()] = binance_symbol.upper()
        logging.info(f"📝 Added token mapping: {token_address} -> {binance_symbol}")
    
    def subscribe_to_price(self, token_address: str, callback: Optional[Callable] = None) -> bool:
        """
        Subscribe to real-time price updates for a token
        Returns True if subscription successful, False otherwise
        """
        binance_symbol = self.get_binance_symbol(token_address)
        if not binance_symbol:
            logging.warning(f"⚠️ No Binance symbol mapping for {token_address}")
            return False
        
        # Add callback if provided
        if callback:
            self.callbacks[binance_symbol].append(callback)
        
        # Add to subscription list
        self.subscribed_symbols.add(binance_symbol)
        
        # Start WebSocket if not running
        if not self.running:
            self.start()
        else:
            # Update subscription for existing connection
            self._update_subscription()
        
        logging.info(f"📡 Subscribed to {binance_symbol} price feed")
        return True
    
    def get_price(self, token_address: str) -> Optional[Decimal]:
        """Get the latest price for a token (WebSocket first, REST fallback)"""
        binance_symbol = self.get_binance_symbol(token_address)
        if not binance_symbol:
            return None

        # Try WebSocket price first
        price_data = self.prices.get(binance_symbol)
        if price_data:
            # Check if price is recent (within last 30 seconds)
            if time.time() - self.last_update.get(binance_symbol, 0) < 30:
                logging.debug(f"📡 WebSocket price for {binance_symbol}: ${price_data:.4f}")
                return Decimal(str(price_data))

        # Fallback to REST API for immediate price
        rest_price = self._get_price_rest_api(binance_symbol)
        if rest_price:
            logging.info(f"🌐 REST API price for {binance_symbol}: ${rest_price:.4f}")
            return rest_price

        return None

    def _get_price_rest_api(self, symbol: str) -> Optional[Decimal]:
        """Get price via Binance REST API as fallback"""
        try:
            # Respect rate limiting
            current_time = time.time()
            if current_time - self.last_request_time < self.min_request_interval:
                time.sleep(self.min_request_interval)

            import requests

            # Use official Binance REST API endpoint
            url = f"{self.rest_base_url}/api/v3/ticker/price"
            params = {'symbol': symbol}

            response = requests.get(url, params=params, timeout=5)
            self.last_request_time = time.time()

            if response.status_code == 200:
                data = response.json()
                price = Decimal(str(data['price']))

                # Cache the price
                self.prices[symbol] = float(price)
                self.last_update[symbol] = time.time()

                return price
            else:
                logging.warning(f"⚠️ Binance REST API error {response.status_code}: {response.text}")
                return None

        except Exception as e:
            logging.error(f"❌ REST API error for {symbol}: {e}")
            return None
    
    def start(self):
        """Start the WebSocket connection"""
        if self.running:
            return
        
        self.running = True
        self.reconnect_attempts = 0
        
        # Start WebSocket in a separate thread
        self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
        self.ws_thread.start()
        
        logging.info("🚀 Binance WebSocket price feed started")
    
    def unsubscribe_from_price(self, token_address: str):
        """Unsubscribe from price updates for a token"""
        try:
            symbol = self.get_binance_symbol(token_address)
            if symbol in self.subscribed_symbols:
                self.subscribed_symbols.remove(symbol)
                logging.info(f"🔇 Unsubscribed from {symbol}")
                return True
            return False
        except Exception as e:
            logging.error(f"Error unsubscribing from {token_address}: {e}")
            return False

    def stop(self):
        """Stop the WebSocket connection"""
        self.running = False
        if self.ws:
            self.ws.close()
        logging.info("🛑 Binance WebSocket price feed stopped")
    
    def _run_websocket(self):
        """Run the WebSocket connection with automatic reconnection"""
        while self.running:
            try:
                self._connect_websocket()
            except Exception as e:
                logging.error(f"❌ WebSocket error: {e}")
                
                if self.reconnect_attempts < self.max_reconnect_attempts:
                    self.reconnect_attempts += 1
                    logging.info(f"🔄 Reconnecting... (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts})")
                    time.sleep(self.reconnect_delay)
                else:
                    logging.error("❌ Max reconnection attempts reached. Stopping WebSocket.")
                    self.running = False
                    break
    
    def _connect_websocket(self):
        """Establish WebSocket connection using official Binance endpoints"""
        if not self.subscribed_symbols:
            logging.warning("⚠️ No symbols to subscribe to")
            return

        # Create stream URL for multiple symbols using official Binance format
        # For multiple streams, use the combined stream endpoint
        if len(self.subscribed_symbols) == 1:
            # Single stream format
            symbol = list(self.subscribed_symbols)[0]
            stream_url = f"{self.ws_base_url}/ws/{symbol.lower()}@ticker"
        else:
            # Multiple streams format
            streams = [f"{symbol.lower()}@ticker" for symbol in self.subscribed_symbols]
            stream_names = '/'.join(streams)
            stream_url = f"{self.ws_base_url}/stream?streams={stream_names}"

        logging.info(f"🔗 Connecting to Binance WebSocket: {stream_url}")
        logging.info(f"📊 Subscribing to {len(self.subscribed_symbols)} symbols: {list(self.subscribed_symbols)}")

        # Add connection timeout and ping settings for stability
        self.ws = websocket.WebSocketApp(
            stream_url,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close,
            on_open=self._on_open
        )

        # Run with ping/pong for connection health
        self.ws.run_forever(ping_interval=20, ping_timeout=10)
    
    def _on_open(self, ws):
        """WebSocket connection opened"""
        logging.info("✅ Binance WebSocket connected")
        self.reconnect_attempts = 0
    
    def _on_message(self, ws, message):
        """Handle incoming price data"""
        try:
            data = json.loads(message)
            
            # Handle single stream data
            if 'stream' in data:
                stream_data = data['data']
                symbol = stream_data['s']  # Symbol (e.g., 'UNIUSDT')
                price = stream_data['c']   # Current price
                
                # Update price cache
                self.prices[symbol] = float(price)
                self.last_update[symbol] = time.time()
                
                # Call registered callbacks
                for callback in self.callbacks.get(symbol, []):
                    try:
                        callback(symbol, Decimal(price))
                    except Exception as e:
                        logging.error(f"❌ Callback error for {symbol}: {e}")
                
                logging.debug(f"📊 {symbol}: ${price}")
            
            # Handle multi-stream data
            elif 's' in data:  # Direct ticker data
                symbol = data['s']
                price = data['c']
                
                self.prices[symbol] = float(price)
                self.last_update[symbol] = time.time()
                
                for callback in self.callbacks.get(symbol, []):
                    try:
                        callback(symbol, Decimal(price))
                    except Exception as e:
                        logging.error(f"❌ Callback error for {symbol}: {e}")
                
                logging.debug(f"📊 {symbol}: ${price}")
                
        except Exception as e:
            logging.error(f"❌ Error processing message: {e}")
    
    def _on_error(self, ws, error):
        """Handle WebSocket errors"""
        logging.error(f"❌ WebSocket error: {error}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        logging.warning(f"🔌 WebSocket closed: {close_status_code} - {close_msg}")
    
    def _update_subscription(self):
        """Update subscription for existing connection"""
        # For simplicity, we'll reconnect when subscriptions change
        if self.ws:
            self.ws.close()
    
    def get_status(self) -> Dict:
        """Get current status of the price feed"""
        return {
            'running': self.running,
            'connected': self.ws is not None and self.ws.sock and self.ws.sock.connected if self.ws else False,
            'subscribed_symbols': list(self.subscribed_symbols),
            'active_prices': len(self.prices),
            'reconnect_attempts': self.reconnect_attempts,
            'latest_prices': {symbol: f"${price:.4f}" for symbol, price in self.prices.items()}
        }

# Global instance for the application
binance_feed = BinanceWebSocketPriceFeed()

def get_realtime_price_binance(token_address: str) -> Optional[Decimal]:
    """
    Enhanced real-time price fetching using Binance WebSocket with auto-initialization
    This is the main function to use for getting prices
    """
    try:
        # Auto-initialize WebSocket if not running
        if not binance_feed.running:
            logging.info("🚀 Auto-starting Binance WebSocket feed...")
            binance_feed.start()
            time.sleep(1)  # Give it a moment to connect

        # Try to get price from WebSocket feed
        price = binance_feed.get_price(token_address)
        if price:
            logging.info(f"📡 Binance WebSocket price for {token_address}: ${price:.4f}")
            return price

        # If not available, try to subscribe and wait briefly
        if binance_feed.subscribe_to_price(token_address):
            logging.info(f"📡 Subscribing to {token_address} price feed...")
            # Wait a moment for initial price data
            time.sleep(3)
            price = binance_feed.get_price(token_address)
            if price:
                logging.info(f"📡 Binance WebSocket price (after subscription) for {token_address}: ${price:.4f}")
                return price

        # If WebSocket fails, try REST API fallback
        logging.info(f"🌐 Trying Binance REST API fallback for {token_address}...")
        rest_price = binance_feed._get_price_rest_api(binance_feed.get_binance_symbol(token_address))
        if rest_price:
            logging.info(f"🌐 Binance REST API price for {token_address}: ${rest_price:.4f}")
            return rest_price

        logging.warning(f"⚠️ No Binance price available for {token_address}")
        return None

    except Exception as e:
        logging.error(f"❌ Error getting Binance price for {token_address}: {e}")
        return None

def start_price_feed_for_tokens(token_addresses: list):
    """Start price feed for a list of tokens"""
    logging.info(f"🚀 Starting Binance price feed for {len(token_addresses)} tokens")
    
    for token_address in token_addresses:
        binance_feed.subscribe_to_price(token_address)
    
    # Give it a moment to establish connections
    time.sleep(3)
    
    status = binance_feed.get_status()
    logging.info(f"📊 Price feed status: {status}")

def stop_price_feed():
    """Stop the price feed"""
    binance_feed.stop()

if __name__ == "__main__":
    # Test the price feed
    logging.basicConfig(level=logging.INFO)
    
    test_tokens = [
        '******************************************',  # UNI
        '******************************************',  # COMP
        '******************************************',  # AAVE
    ]
    
    print("🧪 Testing Binance WebSocket Price Feed")
    
    # Start price feed
    start_price_feed_for_tokens(test_tokens)
    
    # Test price fetching
    for token in test_tokens:
        price = get_realtime_price_binance(token)
        print(f"💰 {token}: ${price:.4f}" if price else f"❌ {token}: No price")
    
    # Keep running for a bit to see live updates
    time.sleep(10)
    
    # Stop feed
    stop_price_feed()
