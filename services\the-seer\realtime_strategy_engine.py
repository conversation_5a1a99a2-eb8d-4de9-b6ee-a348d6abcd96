#!/usr/bin/env python3
"""
Real-time Strategy Engine with Binance WebSocket Integration
===========================================================

Enhanced strategy engine that uses Binance WebSocket for real-time price monitoring,
dynamic pressure score calculation, and instant trade signal generation.

Features:
- Real-time unlock pressure score calculation
- Dynamic market condition analysis
- WebSocket-powered price monitoring
- Instant trade signal generation
- Risk-adjusted position sizing
"""

import os
import sys
import json
import time
import logging
import threading
from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

# Add ledger path for WebSocket access
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'the-ledger'))

from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
from analysis import calculate_unlock_pressure_score, calculate_risk_metrics

@dataclass
class RealTimeSignal:
    """Real-time trading signal with WebSocket data"""
    token_symbol: str
    contract_address: str
    signal_type: str  # 'STRONG_SHORT', 'WEAK_SHORT', 'HOLD', 'AVOID'
    pressure_score: float
    current_price: Decimal
    price_change_1m: float
    price_change_5m: float
    volume_spike: bool
    confidence: float
    timestamp: datetime
    reason: str

class RealTimeStrategyEngine:
    """Enhanced strategy engine with WebSocket integration"""
    
    def __init__(self):
        self.websocket_feed = BinanceWebSocketPriceFeed()
        self.monitored_tokens = {}  # token_address -> token_data
        self.price_history = {}     # token_address -> price_history
        self.signal_callbacks = []  # List of callback functions
        self.running = False
        self.monitoring_thread = None
        
        # Strategy parameters
        self.pressure_threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
        self.volume_spike_threshold = 2.0  # 2x normal volume
        self.price_momentum_window = 300   # 5 minutes
        
        logging.info("🧠 Real-time Strategy Engine initialized")
    
    def add_token_to_monitor(self, token_data: Dict[str, Any]):
        """Add a token to real-time monitoring"""
        contract_address = token_data.get('contract_address')
        token_symbol = token_data.get('token_symbol')
        
        if not contract_address:
            logging.error(f"No contract address for {token_symbol}")
            return False
        
        # Subscribe to WebSocket price feed
        success = self.websocket_feed.subscribe_to_price(contract_address)
        
        if success:
            self.monitored_tokens[contract_address] = token_data
            self.price_history[contract_address] = []
            logging.info(f"📡 Added {token_symbol} to real-time monitoring")
            return True
        else:
            logging.error(f"❌ Failed to subscribe to {token_symbol} WebSocket")
            return False
    
    def remove_token_from_monitor(self, contract_address: str):
        """Remove a token from monitoring"""
        if contract_address in self.monitored_tokens:
            token_symbol = self.monitored_tokens[contract_address].get('token_symbol')
            self.websocket_feed.unsubscribe_from_price(contract_address)
            del self.monitored_tokens[contract_address]
            if contract_address in self.price_history:
                del self.price_history[contract_address]
            logging.info(f"🔇 Removed {token_symbol} from monitoring")
    
    def add_signal_callback(self, callback: Callable[[RealTimeSignal], None]):
        """Add a callback function for trading signals"""
        self.signal_callbacks.append(callback)
        logging.info(f"📞 Added signal callback: {callback.__name__}")
    
    def calculate_realtime_pressure_score(self, token_data: Dict[str, Any], current_price: Decimal) -> float:
        """Calculate pressure score with real-time market data"""
        try:
            # Get base pressure score
            base_score = calculate_unlock_pressure_score(token_data)
            
            # Get real-time market data for adjustments
            contract_address = token_data.get('contract_address')
            price_history = self.price_history.get(contract_address, [])
            
            # Calculate momentum adjustments
            momentum_multiplier = self.calculate_momentum_multiplier(price_history)
            volume_multiplier = self.calculate_volume_multiplier(contract_address)
            
            # Adjust score based on real-time conditions
            adjusted_score = base_score * momentum_multiplier * volume_multiplier
            
            logging.info(f"📊 Real-time pressure score for {token_data.get('token_symbol')}:")
            logging.info(f"   Base Score: {base_score:.4f}")
            logging.info(f"   Momentum Multiplier: {momentum_multiplier:.2f}")
            logging.info(f"   Volume Multiplier: {volume_multiplier:.2f}")
            logging.info(f"   Adjusted Score: {adjusted_score:.4f}")
            
            return adjusted_score
            
        except Exception as e:
            logging.error(f"Error calculating real-time pressure score: {e}")
            return 0.0
    
    def calculate_momentum_multiplier(self, price_history: List[Dict]) -> float:
        """Calculate momentum multiplier based on recent price action"""
        if len(price_history) < 10:
            return 1.0
        
        try:
            # Get recent prices (last 5 minutes)
            recent_prices = [p['price'] for p in price_history[-10:]]
            
            # Calculate momentum
            if len(recent_prices) >= 2:
                price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                
                # Negative momentum (price dropping) increases short signal strength
                if price_change < -0.02:  # >2% drop
                    return 1.5  # Boost signal
                elif price_change < -0.01:  # >1% drop
                    return 1.2
                elif price_change > 0.02:  # >2% rise
                    return 0.7  # Reduce signal
                elif price_change > 0.01:  # >1% rise
                    return 0.9
            
            return 1.0
            
        except Exception as e:
            logging.error(f"Error calculating momentum: {e}")
            return 1.0
    
    def calculate_volume_multiplier(self, contract_address: str) -> float:
        """Calculate volume multiplier based on trading activity"""
        try:
            # Get current volume from WebSocket if available
            # For now, return neutral multiplier
            # TODO: Implement volume spike detection
            return 1.0
            
        except Exception as e:
            logging.error(f"Error calculating volume multiplier: {e}")
            return 1.0
    
    def analyze_token_realtime(self, contract_address: str) -> Optional[RealTimeSignal]:
        """Analyze a token in real-time and generate signals"""
        try:
            if contract_address not in self.monitored_tokens:
                return None
            
            token_data = self.monitored_tokens[contract_address]
            token_symbol = token_data.get('token_symbol')
            
            # Get current price from WebSocket
            current_price = self.websocket_feed.get_price(contract_address)
            if not current_price:
                # Fallback to REST API
                current_price = get_realtime_price_binance(contract_address)
                if not current_price:
                    logging.warning(f"No price data for {token_symbol}")
                    return None
            
            # Update price history
            self.update_price_history(contract_address, current_price)
            
            # Calculate real-time pressure score
            pressure_score = self.calculate_realtime_pressure_score(token_data, current_price)
            
            # Calculate price momentum
            price_change_1m, price_change_5m = self.calculate_price_changes(contract_address)
            
            # Determine signal strength and type
            signal_type, confidence, reason = self.determine_signal_type(
                pressure_score, price_change_1m, price_change_5m, token_data
            )
            
            # Create signal
            signal = RealTimeSignal(
                token_symbol=token_symbol,
                contract_address=contract_address,
                signal_type=signal_type,
                pressure_score=pressure_score,
                current_price=current_price,
                price_change_1m=price_change_1m,
                price_change_5m=price_change_5m,
                volume_spike=False,  # TODO: Implement volume spike detection
                confidence=confidence,
                timestamp=datetime.now(timezone.utc),
                reason=reason
            )
            
            return signal
            
        except Exception as e:
            logging.error(f"Error analyzing token {contract_address}: {e}")
            return None
    
    def update_price_history(self, contract_address: str, price: Decimal):
        """Update price history for a token"""
        if contract_address not in self.price_history:
            self.price_history[contract_address] = []
        
        price_entry = {
            'price': float(price),
            'timestamp': time.time()
        }
        
        self.price_history[contract_address].append(price_entry)
        
        # Keep only last 30 minutes of data
        cutoff_time = time.time() - 1800  # 30 minutes
        self.price_history[contract_address] = [
            p for p in self.price_history[contract_address] 
            if p['timestamp'] > cutoff_time
        ]
    
    def calculate_price_changes(self, contract_address: str) -> tuple:
        """Calculate 1-minute and 5-minute price changes"""
        try:
            price_history = self.price_history.get(contract_address, [])
            if len(price_history) < 2:
                return 0.0, 0.0
            
            current_time = time.time()
            current_price = price_history[-1]['price']
            
            # 1-minute change
            price_1m_ago = None
            for p in reversed(price_history):
                if current_time - p['timestamp'] >= 60:
                    price_1m_ago = p['price']
                    break
            
            # 5-minute change
            price_5m_ago = None
            for p in reversed(price_history):
                if current_time - p['timestamp'] >= 300:
                    price_5m_ago = p['price']
                    break
            
            change_1m = ((current_price - price_1m_ago) / price_1m_ago * 100) if price_1m_ago else 0.0
            change_5m = ((current_price - price_5m_ago) / price_5m_ago * 100) if price_5m_ago else 0.0
            
            return change_1m, change_5m
            
        except Exception as e:
            logging.error(f"Error calculating price changes: {e}")
            return 0.0, 0.0
    
    def determine_signal_type(self, pressure_score: float, change_1m: float, 
                            change_5m: float, token_data: Dict) -> tuple:
        """Determine signal type and confidence based on multiple factors"""
        try:
            # Base signal from pressure score
            if pressure_score < self.pressure_threshold * 0.5:
                return "AVOID", 0.1, "Pressure score too low"
            
            # Strong short signal conditions
            if (pressure_score >= self.pressure_threshold * 1.5 and 
                change_5m < -2.0):  # High pressure + price dropping
                return "STRONG_SHORT", 0.9, f"High pressure ({pressure_score:.2f}) + price declining"
            
            if (pressure_score >= self.pressure_threshold and 
                change_1m < -1.0 and change_5m < -3.0):  # Accelerating decline
                return "STRONG_SHORT", 0.85, "Accelerating price decline with good pressure"
            
            # Medium short signal
            if pressure_score >= self.pressure_threshold:
                if change_5m < -1.0:
                    return "WEAK_SHORT", 0.6, "Good pressure with mild price decline"
                else:
                    return "WEAK_SHORT", 0.5, "Good pressure but price stable"
            
            # Hold/wait conditions
            if pressure_score >= self.pressure_threshold * 0.8:
                return "HOLD", 0.3, "Moderate pressure - monitor for entry"
            
            return "AVOID", 0.2, "Insufficient pressure score"
            
        except Exception as e:
            logging.error(f"Error determining signal type: {e}")
            return "AVOID", 0.0, f"Error: {str(e)}"
    
    def start_monitoring(self):
        """Start real-time monitoring"""
        if self.running:
            logging.warning("Strategy engine already running")
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logging.info("🚀 Real-time strategy engine started")
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.websocket_feed.stop()
        logging.info("🛑 Real-time strategy engine stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                for contract_address in list(self.monitored_tokens.keys()):
                    signal = self.analyze_token_realtime(contract_address)
                    
                    if signal and signal.signal_type in ['STRONG_SHORT', 'WEAK_SHORT']:
                        # Notify all callbacks
                        for callback in self.signal_callbacks:
                            try:
                                callback(signal)
                            except Exception as e:
                                logging.error(f"Error in signal callback: {e}")
                
                # Check every 10 seconds
                time.sleep(10)
                
            except Exception as e:
                logging.error(f"Error in monitoring loop: {e}")
                time.sleep(5)

# Global strategy engine instance
realtime_engine = RealTimeStrategyEngine()

def start_realtime_monitoring(tokens: List[Dict[str, Any]], 
                            signal_callback: Callable[[RealTimeSignal], None]):
    """Start real-time monitoring for a list of tokens"""
    # Add signal callback
    realtime_engine.add_signal_callback(signal_callback)
    
    # Add tokens to monitor
    for token in tokens:
        realtime_engine.add_token_to_monitor(token)
    
    # Start monitoring
    realtime_engine.start_monitoring()
    
    return realtime_engine

def stop_realtime_monitoring():
    """Stop real-time monitoring"""
    realtime_engine.stop_monitoring()
