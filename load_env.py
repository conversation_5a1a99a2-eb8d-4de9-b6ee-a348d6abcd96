#!/usr/bin/env python3
"""
Environment Variable Loader
===========================

Simple utility to load environment variables from .env file.
"""

import os
from pathlib import Path

def load_env_file(env_path: str = ".env"):
    """Load environment variables from .env file"""
    env_file = Path(env_path)
    
    if not env_file.exists():
        print(f"⚠️ Environment file not found: {env_path}")
        return False
    
    try:
        with open(env_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Parse key=value pairs
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # Set environment variable
                    os.environ[key] = value
                else:
                    print(f"⚠️ Invalid line {line_num} in {env_path}: {line}")
        
        print(f"✅ Loaded environment variables from {env_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error loading {env_path}: {e}")
        return False

# Auto-load .env file when this module is imported
if __name__ != "__main__":
    load_env_file()

if __name__ == "__main__":
    # Test the loader
    success = load_env_file()
    
    if success:
        print("\n📋 Loaded Environment Variables:")
        for key, value in os.environ.items():
            if any(keyword in key.upper() for keyword in ['DATABASE', 'API', 'KEY', 'TOKEN', 'URL']):
                # Mask sensitive values
                if 'KEY' in key.upper() or 'TOKEN' in key.upper():
                    masked_value = value[:8] + "..." if len(value) > 8 else "***"
                    print(f"   {key}={masked_value}")
                else:
                    print(f"   {key}={value}")
    else:
        print("❌ Failed to load environment variables")
