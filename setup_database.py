#!/usr/bin/env python3
"""
Database Setup Script for Project Chimera
=========================================

This script sets up the database for your trading system with support for:
- SQLite (for local development and testing)
- PostgreSQL (for production)
- Automatic schema creation
- Sample data insertion for testing
"""

import os
import sys
import logging
import sqlite3
from pathlib import Path
from urllib.parse import urlparse

def setup_logging():
    """Setup logging for database operations"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def get_database_config():
    """Get database configuration from environment"""
    database_url = os.environ.get('DATABASE_URL')
    
    if not database_url:
        # Default to SQLite if no DATABASE_URL is set
        database_url = 'sqlite:///./chimera_trading.db'
        print("⚠️ No DATABASE_URL found, using SQLite default")
    
    parsed = urlparse(database_url)
    
    return {
        'url': database_url,
        'scheme': parsed.scheme,
        'host': parsed.hostname,
        'port': parsed.port,
        'database': parsed.path.lstrip('/') if parsed.path else None,
        'username': parsed.username,
        'password': parsed.password
    }

def create_sqlite_database(db_path: str):
    """Create SQLite database with required tables"""
    try:
        print(f"🗄️ Setting up SQLite database: {db_path}")
        
        # Create database directory if it doesn't exist
        db_dir = Path(db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        # Connect to SQLite database (creates if doesn't exist)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Read and execute SQLite-specific schema
        sqlite_schema_path = Path(__file__).parent / 'database' / 'sqlite_schema.sql'
        schema_path = Path(__file__).parent / 'database' / 'schema.sql'

        if sqlite_schema_path.exists():
            print("📋 Loading SQLite schema from sqlite_schema.sql...")
            with open(sqlite_schema_path, 'r') as f:
                schema_sql = f.read()

            # Execute schema (split by semicolon for multiple statements)
            for statement in schema_sql.split(';'):
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except sqlite3.Error as e:
                        if "already exists" not in str(e):
                            print(f"⚠️ Schema warning: {e}")
        else:
            print("📋 Creating basic schema...")
            # Create basic tables if sqlite_schema.sql doesn't exist
            create_basic_schema(cursor)
        
        conn.commit()
        
        # Test the connection
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✅ SQLite database created successfully!")
        print(f"   Database: {db_path}")
        print(f"   Tables: {[table[0] for table in tables]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating SQLite database: {e}")
        return False

def create_basic_schema(cursor):
    """Create basic schema if schema.sql doesn't exist"""
    
    # Unlock events table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS unlock_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token_symbol VARCHAR(20) NOT NULL,
            contract_address VARCHAR(42) NOT NULL,
            unlock_date TIMESTAMP NOT NULL,
            unlock_amount DECIMAL NOT NULL,
            circulating_supply DECIMAL,
            total_supply DECIMAL,
            pressure_score DECIMAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(contract_address, unlock_date)
        )
    ''')
    
    # Positions table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS positions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_id INTEGER UNIQUE,
            token_symbol VARCHAR(20) NOT NULL,
            contract_address VARCHAR(42) NOT NULL,
            strategy_id VARCHAR(50),
            amount_borrowed DECIMAL NOT NULL,
            entry_price_in_usdc DECIMAL NOT NULL,
            stop_loss_price DECIMAL,
            take_profit_price DECIMAL,
            status VARCHAR(20) DEFAULT 'OPEN',
            borrow_tx_hash VARCHAR(66),
            swap_tx_hash VARCHAR(66),
            close_tx_hash VARCHAR(66),
            opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            closed_at TIMESTAMP,
            pnl_usd DECIMAL,
            close_reason TEXT
        )
    ''')
    
    # Trade history table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS trade_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            position_id INTEGER,
            action VARCHAR(20) NOT NULL,
            token_symbol VARCHAR(20),
            amount DECIMAL,
            price DECIMAL,
            tx_hash VARCHAR(66),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT
        )
    ''')
    
    print("📋 Basic schema created")

def setup_postgresql_database(config: dict):
    """Setup PostgreSQL database"""
    try:
        import psycopg2
        from psycopg2 import sql
        
        print(f"🗄️ Setting up PostgreSQL database...")
        print(f"   Host: {config['host']}:{config['port']}")
        print(f"   Database: {config['database']}")
        
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            database=config['database'],
            user=config['username'],
            password=config['password']
        )
        
        cursor = conn.cursor()
        
        # Read and execute schema
        schema_path = Path(__file__).parent / 'database' / 'schema.sql'
        
        if schema_path.exists():
            print("📋 Loading schema from schema.sql...")
            with open(schema_path, 'r') as f:
                schema_sql = f.read()
            cursor.execute(schema_sql)
        else:
            print("❌ schema.sql not found")
            return False
        
        conn.commit()
        
        # Test the connection
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
        tables = cursor.fetchall()
        
        print(f"✅ PostgreSQL database setup successfully!")
        print(f"   Tables: {[table[0] for table in tables]}")
        
        conn.close()
        return True
        
    except ImportError:
        print("❌ psycopg2 not installed. Install with: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ Error setting up PostgreSQL: {e}")
        return False

def insert_sample_data(config: dict):
    """Insert sample data for testing"""
    try:
        if config['scheme'] == 'sqlite':
            conn = sqlite3.connect(config['url'].replace('sqlite:///', ''))
            cursor = conn.cursor()
            
            # Insert sample unlock events
            sample_events = [
                ('UNI', '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984', '2025-01-30 12:00:00', 1000000, 750000000, 1000000000, 0.8),
                ('AAVE', '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9', '2025-02-01 15:00:00', 500000, 14000000, 16000000, 0.9),
                ('COMP', '0xc00e94cb662c3520282e6f5717214004a7f26888', '2025-01-31 10:00:00', 100000, 10000000, 10000000, 0.7)
            ]
            
            for event in sample_events:
                try:
                    cursor.execute('''
                        INSERT OR IGNORE INTO unlock_events 
                        (token_symbol, contract_address, unlock_date, unlock_amount, circulating_supply, total_supply, pressure_score)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', event)
                except sqlite3.Error as e:
                    print(f"⚠️ Sample data warning: {e}")
            
            conn.commit()
            conn.close()
            
            print("✅ Sample data inserted successfully!")
            
        else:
            print("ℹ️ Sample data insertion only supported for SQLite in this script")
            
    except Exception as e:
        print(f"❌ Error inserting sample data: {e}")

def test_database_connection(config: dict):
    """Test database connection and basic operations"""
    try:
        print("🧪 Testing database connection...")
        
        if config['scheme'] == 'sqlite':
            conn = sqlite3.connect(config['url'].replace('sqlite:///', ''))
            cursor = conn.cursor()
            
            # Test query
            cursor.execute("SELECT COUNT(*) FROM unlock_events")
            count = cursor.fetchone()[0]
            
            print(f"✅ Database connection test passed!")
            print(f"   Unlock events in database: {count}")
            
            conn.close()
            return True
            
        elif config['scheme'] == 'postgresql':
            import psycopg2
            
            conn = psycopg2.connect(
                host=config['host'],
                port=config['port'],
                database=config['database'],
                user=config['username'],
                password=config['password']
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM unlock_events")
            count = cursor.fetchone()[0]
            
            print(f"✅ Database connection test passed!")
            print(f"   Unlock events in database: {count}")
            
            conn.close()
            return True
            
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def main():
    """Main database setup function"""
    setup_logging()
    
    print("🗄️ Project Chimera Database Setup")
    print("=" * 40)
    
    # Get database configuration
    config = get_database_config()
    
    print(f"Database Type: {config['scheme']}")
    print(f"Database URL: {config['url']}")
    print()
    
    # Setup database based on type
    if config['scheme'] == 'sqlite':
        db_path = config['url'].replace('sqlite:///', '')
        success = create_sqlite_database(db_path)
        
        if success:
            insert_sample_data(config)
            test_database_connection(config)
            
            print("\n🎉 SQLite Database Setup Complete!")
            print("=" * 40)
            print("Your trading system is now ready to use!")
            print(f"Database file: {os.path.abspath(db_path)}")
            print("\nNext steps:")
            print("1. Run: python demo_websocket_trading_integration.py")
            print("2. Run: python run_realtime_trading_system.py")
            
    elif config['scheme'] == 'postgresql':
        success = setup_postgresql_database(config)
        
        if success:
            test_database_connection(config)
            
            print("\n🎉 PostgreSQL Database Setup Complete!")
            print("=" * 40)
            print("Your trading system is now ready to use!")
            
    else:
        print(f"❌ Unsupported database type: {config['scheme']}")
        print("Supported types: sqlite, postgresql")
        return False
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
